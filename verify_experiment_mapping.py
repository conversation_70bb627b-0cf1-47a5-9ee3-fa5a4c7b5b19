#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证结果题目是否按照实验组ID从源题目中获取
"""

import json
import re

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def parse_experiment_groups():
    """正确解析实验组规则"""
    groups = {"en": [], "pt": []}
    
    try:
        with open('实验组.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析英语实验组 - 处理前导零
        en_matches = re.findall(r'英语语言建议（([^）]+)）', content)
        for match in en_matches:
            # 分割并去除前导零
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["en"].append(ids)
        
        # 解析葡萄牙语实验组 - 处理前导零
        pt_matches = re.findall(r'葡萄牙语言建议（([^）]+)）', content)
        for match in pt_matches:
            # 分割并去除前导零
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["pt"].append(ids)
            
    except Exception as e:
        print(f"Error parsing experiment groups: {e}")
    
    return groups

def create_source_mapping(source_data):
    """创建源数据映射"""
    mapping = {}
    for item in source_data:
        mapping[item["id"]] = item
    return mapping

def verify_single_group(file_path, group_name, expected_ids, source_mapping):
    """验证单个组的映射关系"""
    print(f"\n=== {group_name} 验证 ===")
    result_data = load_json_file(file_path)
    
    if not result_data:
        print("无法加载数据")
        return False
    
    print(f"文件: {file_path}")
    print(f"题目总数: {len(result_data)}")
    print(f"期望ID: {expected_ids}")
    print(f"期望题目数: {len(expected_ids)}")
    
    # 检查题目数量是否匹配
    if len(result_data) != len(expected_ids):
        print(f"❌ 题目数量不匹配: 期望{len(expected_ids)}个，实际{len(result_data)}个")
        return False
    
    # 分析每个题目的映射关系
    mapping_correct = True
    difficulty_ids = []
    replace_ids = []
    
    for i, item in enumerate(result_data):
        difficulty_id = str(item.get("difficulty", ""))
        replace_id = str(item.get("replaceId", ""))
        expected_id = expected_ids[i]
        
        difficulty_ids.append(difficulty_id)
        replace_ids.append(replace_id)
        
        print(f"\n题目 {i+1}:")
        print(f"  期望ID: {expected_id}")
        print(f"  difficulty: {difficulty_id}")
        print(f"  replaceId: {replace_id}")
        
        # 检查哪个字段匹配期望ID
        difficulty_matches = difficulty_id == expected_id
        replace_id_matches = replace_id == expected_id
        
        print(f"  difficulty匹配: {difficulty_matches}")
        print(f"  replaceId匹配: {replace_id_matches}")
        
        # 验证题目内容是否从正确的源ID获取
        if difficulty_id in source_mapping:
            source_item = source_mapping[difficulty_id]
            content_matches = source_item.get("question", "") == item.get("quizTitle", "")
            answer_matches = source_item.get("answer", "") == item.get("rightAnswer", "")
            print(f"  difficulty对应源题目内容匹配: {content_matches}")
            print(f"  difficulty对应源题目答案匹配: {answer_matches}")
            
            if difficulty_matches and content_matches and answer_matches:
                print(f"  ✅ 通过difficulty字段正确获取")
            elif not difficulty_matches:
                print(f"  ⚠️ difficulty字段不匹配期望ID")
        
        if replace_id in source_mapping and replace_id != difficulty_id:
            source_item = source_mapping[replace_id]
            print(f"  replaceId对应源题目: {source_item.get('question', '')[:30]}...")
            
            if replace_id_matches:
                print(f"  ⚠️ replaceId匹配期望ID，但题目内容来自difficulty字段")
        
        if not (difficulty_matches or replace_id_matches):
            print(f"  ❌ 没有字段匹配期望ID")
            mapping_correct = False
    
    # 总结分析
    print(f"\n=== {group_name} 总结 ===")
    print(f"期望ID序列: {expected_ids}")
    print(f"difficulty序列: {difficulty_ids}")
    print(f"replaceId序列: {replace_ids}")
    
    # 检查整体匹配情况
    difficulty_set_matches = set(difficulty_ids) == set(expected_ids)
    replace_id_set_matches = set(replace_ids) == set(expected_ids)
    difficulty_order_matches = difficulty_ids == expected_ids
    replace_id_order_matches = replace_ids == expected_ids
    
    print(f"\ndifficulty字段集合匹配: {difficulty_set_matches}")
    print(f"difficulty字段顺序匹配: {difficulty_order_matches}")
    print(f"replaceId字段集合匹配: {replace_id_set_matches}")
    print(f"replaceId字段顺序匹配: {replace_id_order_matches}")
    
    if difficulty_order_matches:
        print("✅ 结果题目按照实验组ID顺序，通过difficulty字段从源题目获取")
        return True
    elif replace_id_order_matches:
        print("✅ 结果题目按照实验组ID顺序，通过replaceId字段标记")
        return True
    elif difficulty_set_matches:
        print("⚠️ difficulty字段包含所有期望ID，但顺序不匹配")
        return False
    elif replace_id_set_matches:
        print("⚠️ replaceId字段包含所有期望ID，但顺序不匹配")
        return False
    else:
        print("❌ 没有字段完全匹配实验组要求")
        return False

def main():
    print("=== 验证结果题目是否按照实验组ID从源题目获取 ===")
    
    # 1. 解析实验组规则
    experiment_groups = parse_experiment_groups()
    print(f"\n实验组规则:")
    for i, group in enumerate(experiment_groups['en']):
        print(f"英语组{chr(65+i)}: {group}")
    for i, group in enumerate(experiment_groups['pt']):
        print(f"葡萄牙语组{chr(65+i)}: {group}")
    
    # 2. 加载源数据
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    en_source_mapping = create_source_mapping(en_source)
    pt_source_mapping = create_source_mapping(pt_source)
    
    # 3. 验证每个组
    results = []
    
    # 英语组
    for i, expected_ids in enumerate(experiment_groups['en']):
        group_name = f"英语{chr(65+i)}组"
        file_path = f"结果题目/en_{chr(65+i)}.json"
        result = verify_single_group(file_path, group_name, expected_ids, en_source_mapping)
        results.append((group_name, result))
    
    # 葡萄牙语组
    for i, expected_ids in enumerate(experiment_groups['pt']):
        group_name = f"葡萄牙语{chr(65+i)}组"
        file_path = f"结果题目/pt_{chr(65+i)}.json"
        result = verify_single_group(file_path, group_name, expected_ids, pt_source_mapping)
        results.append((group_name, result))
    
    # 4. 最终总结
    print(f"\n=== 最终验证结果 ===")
    all_correct = True
    for group_name, result in results:
        status = "✅ 正确" if result else "❌ 不正确"
        print(f"{group_name}: {status}")
        if not result:
            all_correct = False
    
    if all_correct:
        print(f"\n🎉 所有结果题目都按照实验组ID从源题目正确获取！")
    else:
        print(f"\n⚠️ 部分结果题目的ID映射存在问题")

if __name__ == "__main__":
    main()
