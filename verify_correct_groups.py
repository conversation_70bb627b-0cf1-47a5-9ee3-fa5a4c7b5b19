#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新验证实验组配置：英语5组，葡萄牙语3组
"""

import json
import re

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def parse_experiment_groups_correct():
    """正确解析实验组规则：英语5组，葡萄牙语3组"""
    groups = {"en": [], "pt": []}
    
    try:
        with open('实验组.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("原始文件内容:")
        print(content)
        print("\n" + "="*50 + "\n")
        
        # 解析英语实验组 - 应该有5组
        en_matches = re.findall(r'英语语言建议（([^）]+)）', content)
        print(f"找到英语组数量: {len(en_matches)}")
        
        for i, match in enumerate(en_matches):
            print(f"英语组{i+1}原始: {match}")
            # 处理特殊字符和格式
            cleaned_match = match.replace('，', ',').replace(' ', '').replace('，', ',')
            # 分割并处理前导零和特殊字符
            ids = []
            for id_str in cleaned_match.split(','):
                id_str = id_str.strip()
                if id_str and id_str.isdigit():
                    ids.append(str(int(id_str)))  # 去除前导零
                elif id_str:
                    print(f"  警告: 无法解析的ID '{id_str}'")
            groups["en"].append(ids)
            print(f"英语组{i+1}解析后: {ids}")
        
        print(f"\n葡萄牙语组解析:")
        # 解析葡萄牙语实验组 - 应该有3组
        pt_matches = re.findall(r'葡萄牙语言建议（([^）]+)）', content)
        print(f"找到葡萄牙语组数量: {len(pt_matches)}")
        
        for i, match in enumerate(pt_matches):
            print(f"葡萄牙语组{i+1}原始: {match}")
            # 处理特殊字符和格式
            cleaned_match = match.replace('，', ',').replace(' ', '')
            # 分割并处理前导零
            ids = []
            for id_str in cleaned_match.split(','):
                id_str = id_str.strip()
                if id_str and id_str.isdigit():
                    ids.append(str(int(id_str)))  # 去除前导零
                elif id_str:
                    print(f"  警告: 无法解析的ID '{id_str}'")
            groups["pt"].append(ids)
            print(f"葡萄牙语组{i+1}解析后: {ids}")
            
    except Exception as e:
        print(f"Error parsing experiment groups: {e}")
    
    return groups

def create_source_mapping(source_data):
    """创建源数据映射"""
    mapping = {}
    for item in source_data:
        mapping[item["id"]] = item
    return mapping

def parse_error_field(error_str):
    """解析error字段中的JSON数组"""
    try:
        import ast
        return ast.literal_eval(error_str)
    except:
        try:
            return json.loads(error_str)
        except:
            return []

def verify_group_mapping(file_path, group_name, expected_ids, source_mapping):
    """验证单个组的映射关系"""
    print(f"\n=== {group_name} 验证 ===")
    result_data = load_json_file(file_path)
    
    if not result_data:
        print(f"❌ 无法加载文件: {file_path}")
        return False
    
    print(f"文件: {file_path}")
    print(f"题目总数: {len(result_data)}")
    print(f"期望ID序列: {expected_ids}")
    print(f"期望题目数: {len(expected_ids)}")
    
    # 检查题目数量是否匹配
    if len(result_data) != len(expected_ids):
        print(f"❌ 题目数量不匹配: 期望{len(expected_ids)}个，实际{len(result_data)}个")
        return False
    
    # 验证每个题目
    all_correct = True
    for i, (item, expected_id) in enumerate(zip(result_data, expected_ids)):
        result_title = item.get("quizTitle", "")
        result_answer = item.get("rightAnswer", "")
        
        print(f"\n--- 题目 {i+1} (期望ID: {expected_id}) ---")
        
        if expected_id in source_mapping:
            source_item = source_mapping[expected_id]
            source_question = source_item.get("question", "")
            source_error = source_item.get("error", "")
            
            # 解析error字段
            error_options = parse_error_field(source_error)
            
            # 验证映射关系
            title_matches = source_question == result_title
            answer_in_error = result_answer in error_options if error_options else False
            
            print(f"题目匹配: {title_matches}")
            print(f"答案在选项中: {answer_in_error}")
            
            if title_matches and answer_in_error:
                print("✅ 正确")
            else:
                print("❌ 不匹配")
                all_correct = False
                if not title_matches:
                    print(f"  期望题目: {source_question[:50]}...")
                    print(f"  实际题目: {result_title[:50]}...")
                if not answer_in_error:
                    print(f"  期望选项: {error_options}")
                    print(f"  实际答案: {result_answer}")
        else:
            print(f"❌ 期望ID {expected_id} 在源数据中不存在")
            all_correct = False
    
    return all_correct

def main():
    print("=== 重新验证实验组配置：英语5组，葡萄牙语3组 ===\n")
    
    # 1. 解析实验组规则
    experiment_groups = parse_experiment_groups_correct()
    
    print(f"\n最终解析结果:")
    print(f"英语组数量: {len(experiment_groups['en'])}")
    for i, group in enumerate(experiment_groups['en']):
        print(f"  英语组{chr(65+i)}: {group} ({len(group)}个题目)")
    
    print(f"葡萄牙语组数量: {len(experiment_groups['pt'])}")
    for i, group in enumerate(experiment_groups['pt']):
        print(f"  葡萄牙语组{chr(65+i)}: {group} ({len(group)}个题目)")
    
    # 2. 加载源数据
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    en_source_mapping = create_source_mapping(en_source)
    pt_source_mapping = create_source_mapping(pt_source)
    
    print(f"\n源数据:")
    print(f"英语源题目: {len(en_source)}个")
    print(f"葡萄牙语源题目: {len(pt_source)}个")
    
    # 3. 验证英语组
    en_results = []
    group_labels = ['A', 'B', 'C', 'D', 'E']
    
    for i, expected_ids in enumerate(experiment_groups['en']):
        if i < len(group_labels):
            group_name = f"英语{group_labels[i]}组"
            file_path = f"结果题目/en_{group_labels[i]}.json"
            result = verify_group_mapping(file_path, group_name, expected_ids, en_source_mapping)
            en_results.append((group_name, result))
    
    # 4. 验证葡萄牙语组
    pt_results = []
    for i, expected_ids in enumerate(experiment_groups['pt']):
        if i < 3:  # A, B, C
            group_name = f"葡萄牙语{group_labels[i]}组"
            file_path = f"结果题目/pt_{group_labels[i]}.json"
            result = verify_group_mapping(file_path, group_name, expected_ids, pt_source_mapping)
            pt_results.append((group_name, result))
    
    # 5. 总结
    print(f"\n=== 最终验证结果 ===")
    
    print("英语组:")
    for group_name, result in en_results:
        status = "✅ 正确" if result else "❌ 不正确"
        print(f"  {group_name}: {status}")
    
    print("葡萄牙语组:")
    for group_name, result in pt_results:
        status = "✅ 正确" if result else "❌ 不正确"
        print(f"  {group_name}: {status}")
    
    all_results = en_results + pt_results
    all_correct = all(result for _, result in all_results)
    
    if all_correct:
        print(f"\n🎉 所有组都验证通过！")
    else:
        failed_count = sum(1 for _, result in all_results if not result)
        print(f"\n⚠️ {failed_count}个组存在问题")

if __name__ == "__main__":
    main()
