# 实验数据验证报告 - 最终版

## 概述
本报告分析了答题数据实验库中的实验数据是否符合实验组的要求，并验证了数据从源题目中的正确获取。

## 实验组规则
根据`实验组.txt`文件，实验组规则如下：

### 英语实验组
- **A组**: [21, 9, 22] (3个题目)
- **B组**: [28, 35, 14, 32, 47, 22, 37, 1, 12, 6] (10个题目)  
- **C组**: [28, 32, 37, 1, 35, 47, 14, 6, 7, 9] (10个题目)

### 葡萄牙语实验组
- **A组**: [16, 7, 13] (3个题目)
- **B组**: [2, 1, 7, 3, 6, 15, 4, 10, 16, 5] (10个题目)
- **C组**: [2, 3, 4, 5, 1, 6, 7, 8, 10, 15] (10个题目)

## 数据映射关系
根据分析，源数据和结果题目存在以下映射关系：
- `answer` → `rightAnswer` (正确答案)
- `question` → `quizTitle` (题目标题)
- `error` → `quizAnswer` (答案选项)
- `ref` → `quizRef` (参考文献)
- `id` → `difficulty` (题目ID，用于匹配实验组规则)

## 关键发现
在分析过程中发现了一个重要的数据结构特点：
- **`difficulty`字段**: 对应源题目的`id`，用于匹配实验组规则
- **`replaceId`字段**: 另一个ID字段，但不是用于实验组匹配的主要字段

## 验证结果

### 英语组验证结果
- **A组**: ✅ 完全符合要求
  - 期望3个题目，实际3个题目
  - ID匹配: [21, 9, 22]
  - 所有答案正确匹配源题目
  - 字段映射完全正确
  
- **B组**: ✅ 完全符合要求
  - 期望10个题目，实际10个题目
  - ID匹配: [28, 35, 14, 32, 47, 22, 37, 1, 12, 6]
  - 所有答案正确匹配源题目
  - 字段映射完全正确
  
- **C组**: ✅ 完全符合要求
  - 期望10个题目，实际10个题目
  - ID匹配: [28, 32, 37, 1, 35, 47, 14, 6, 7, 9]
  - 所有答案正确匹配源题目
  - 字段映射完全正确

### 葡萄牙语组验证结果
- **A组**: ✅ 完全符合要求
  - 期望3个题目，实际3个题目
  - ID匹配: [16, 7, 13]
  - 所有答案正确匹配源题目
  - 字段映射完全正确
  
- **B组**: ✅ 完全符合要求
  - 期望10个题目，实际10个题目
  - ID匹配: [2, 1, 7, 3, 6, 15, 4, 10, 16, 5]
  - 所有答案正确匹配源题目
  - 字段映射完全正确
  
- **C组**: ✅ 完全符合要求
  - 期望10个题目，实际10个题目
  - ID匹配: [2, 3, 4, 5, 1, 6, 7, 8, 10, 15]
  - 所有答案正确匹配源题目
  - 字段映射完全正确

## 数据质量验证

### 示例验证 - 英语A组第一题
**实验组要求**: ID 21
**源题目** (en.json, id="21"):
```json
{
  "answer": "Mary",
  "question": "Jesus' human mother was ___.",
  "ref": "Luke 2:5-7",
  "error": "[\"Esther\",\"Eve\",\"Hannah\",\"Mary\"]"
}
```

**结果题目** (en_A.json):
```json
{
  "difficulty": 21,
  "rightAnswer": "Mary",
  "quizTitle": "Jesus' human mother was ___.",
  "quizRef": "Luke 2:5-7",
  "quizAnswer": ["Esther", "Eve", "Hannah", "Mary"]
}
```

✅ **完全匹配**: 所有字段都正确映射

### 示例验证 - 葡萄牙语A组第一题
**实验组要求**: ID 16
**源题目** (pt.json, id="16"):
```json
{
  "answer": "Mary",
  "question": "Jesus' mãe humana era ___.",
  "ref": "Luke 2:5-7",
  "error": "[\"Ester\",\"Véspera\",\"Hannah\",\"Mary\"]"
}
```

**结果题目** (pt_A.json):
```json
{
  "difficulty": 16,
  "rightAnswer": "Mary",
  "quizTitle": "Jesus' mãe humana era ___.",
  "quizRef": "Luke 2:5-7",
  "quizAnswer": ["Ester", "Véspera", "Hannah", "Mary"]
}
```

✅ **完全匹配**: 所有字段都正确映射

## 数据统计总结

| 语言 | 组别 | 题目数量 | ID匹配率 | 答案正确率 | 字段映射正确率 |
|------|------|----------|----------|------------|----------------|
| 英语 | A组  | 3        | 100%     | 100%       | 100%           |
| 英语 | B组  | 10       | 100%     | 100%       | 100%           |
| 英语 | C组  | 10       | 100%     | 100%       | 100%           |
| 葡萄牙语 | A组 | 3     | 100%     | 100%       | 100%           |
| 葡萄牙语 | B组 | 10    | 100%     | 100%       | 100%           |
| 葡萄牙语 | C组 | 10    | 100%     | 100%       | 100%           |

## 总结
✅ **所有验证都通过**

1. **ID匹配**: 所有结果题目中的`difficulty`字段都完全匹配实验组规则中指定的ID
2. **数据完整性**: 没有缺失或多余的题目
3. **字段映射**: 所有字段都正确从源题目映射到结果题目
4. **答案验证**: 所有答案都正确匹配源题目中的答案
5. **数据一致性**: 英语和葡萄牙语两种语言的数据都完全符合要求

**结论**: 实验数据完全符合实验组的要求，数据从源题目中正确获取并映射。实验库的数据质量优秀，可以用于实验研究。

## 技术说明
- 分析使用Python脚本进行自动化验证
- 验证了所有6个结果文件（英语ABC组，葡萄牙语ABC组）
- 总共验证了46个题目（英语23个，葡萄牙语23个）
- 所有验证项目都100%通过
