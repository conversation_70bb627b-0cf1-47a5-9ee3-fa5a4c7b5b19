#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新验证结果题目的数据映射关系
检查是否按照实验组ID获取，使用正确的字段映射：
- 源题目.question → 结果题目.quizTitle  
- 源题目.error → 结果题目.rightAnswer
"""

import json
import re

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def parse_experiment_groups():
    """解析实验组规则"""
    groups = {"en": [], "pt": []}
    
    try:
        with open('实验组.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析英语实验组
        en_matches = re.findall(r'英语语言建议（([^）]+)）', content)
        for match in en_matches:
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["en"].append(ids)
        
        # 解析葡萄牙语实验组
        pt_matches = re.findall(r'葡萄牙语言建议（([^）]+)）', content)
        for match in pt_matches:
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["pt"].append(ids)
            
    except Exception as e:
        print(f"Error parsing experiment groups: {e}")
    
    return groups

def create_source_mapping(source_data):
    """创建源数据映射"""
    mapping = {}
    for item in source_data:
        mapping[item["id"]] = item
    return mapping

def parse_error_field(error_str):
    """解析error字段中的JSON数组"""
    try:
        import ast
        return ast.literal_eval(error_str)
    except:
        try:
            return json.loads(error_str)
        except:
            return []

def verify_experiment_mapping(file_path, group_name, expected_ids, source_mapping):
    """验证实验组映射关系"""
    print(f"\n=== {group_name} 验证 ===")
    result_data = load_json_file(file_path)
    
    if not result_data:
        print("无法加载数据")
        return False
    
    print(f"文件: {file_path}")
    print(f"题目总数: {len(result_data)}")
    print(f"期望ID序列: {expected_ids}")
    
    # 检查是否按照实验组ID顺序获取
    mapping_correct = True
    mismatches = []
    
    for i, (item, expected_id) in enumerate(zip(result_data, expected_ids)):
        print(f"\n--- 题目 {i+1} (期望ID: {expected_id}) ---")
        
        result_title = item.get("quizTitle", "")
        result_answer = item.get("rightAnswer", "")
        
        # 检查是否从期望ID获取
        if expected_id in source_mapping:
            source_item = source_mapping[expected_id]
            source_question = source_item.get("question", "")
            source_error = source_item.get("error", "")
            
            # 解析error字段
            error_options = parse_error_field(source_error)
            
            print(f"期望源题目 (ID={expected_id}):")
            print(f"  question: {source_question[:50]}...")
            print(f"  error: {source_error}")
            print(f"结果题目:")
            print(f"  quizTitle: {result_title[:50]}...")
            print(f"  rightAnswer: {result_answer}")
            
            # 验证映射关系
            title_matches = source_question == result_title
            answer_in_error = result_answer in error_options if error_options else False
            
            print(f"题目匹配: {title_matches}")
            print(f"答案在error选项中: {answer_in_error}")
            
            if title_matches and answer_in_error:
                print("✅ 正确按照实验组ID获取")
            else:
                print("❌ 不匹配实验组ID")
                mapping_correct = False
                mismatches.append({
                    "position": i+1,
                    "expected_id": expected_id,
                    "title_match": title_matches,
                    "answer_match": answer_in_error
                })
        else:
            print(f"❌ 期望ID {expected_id} 在源数据中不存在")
            mapping_correct = False
            mismatches.append({
                "position": i+1,
                "expected_id": expected_id,
                "error": "ID not found in source"
            })
    
    # 总结
    print(f"\n=== {group_name} 总结 ===")
    if mapping_correct:
        print("✅ 所有题目都正确按照实验组ID从源题目获取")
    else:
        print(f"❌ 发现 {len(mismatches)} 个不匹配项:")
        for mismatch in mismatches:
            print(f"  位置 {mismatch['position']}: 期望ID {mismatch['expected_id']} - {mismatch}")
    
    return mapping_correct

def find_actual_source_mapping(file_path, source_mapping):
    """找出结果题目实际来自哪个源题目"""
    print(f"\n=== 查找实际映射关系: {file_path} ===")
    result_data = load_json_file(file_path)
    
    actual_mappings = []
    
    for i, item in enumerate(result_data):
        result_title = item.get("quizTitle", "")
        result_answer = item.get("rightAnswer", "")
        
        found_source = None
        
        # 遍历所有源题目寻找匹配
        for source_id, source_item in source_mapping.items():
            source_question = source_item.get("question", "")
            source_error = source_item.get("error", "")
            error_options = parse_error_field(source_error)
            
            title_matches = source_question == result_title
            answer_in_error = result_answer in error_options if error_options else False
            
            if title_matches and answer_in_error:
                found_source = source_id
                break
        
        actual_mappings.append({
            "position": i+1,
            "result_title": result_title[:30] + "...",
            "result_answer": result_answer,
            "actual_source_id": found_source
        })
        
        print(f"题目 {i+1}: 来自源题目ID {found_source}")
    
    return actual_mappings

def main():
    print("=== 重新验证结果题目的正确映射关系 ===")
    print("映射规则: 源题目.question → 结果题目.quizTitle")
    print("映射规则: 源题目.error → 结果题目.rightAnswer")
    
    # 1. 解析实验组规则
    experiment_groups = parse_experiment_groups()
    print(f"\n实验组规则:")
    for i, group in enumerate(experiment_groups['en']):
        print(f"英语组{chr(65+i)}: {group}")
    for i, group in enumerate(experiment_groups['pt']):
        print(f"葡萄牙语组{chr(65+i)}: {group}")
    
    # 2. 加载源数据
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    en_source_mapping = create_source_mapping(en_source)
    pt_source_mapping = create_source_mapping(pt_source)
    
    # 3. 验证每个组是否按照实验组ID获取
    results = []
    
    # 英语组
    for i, expected_ids in enumerate(experiment_groups['en']):
        group_name = f"英语{chr(65+i)}组"
        file_path = f"结果题目/en_{chr(65+i)}.json"
        result = verify_experiment_mapping(file_path, group_name, expected_ids, en_source_mapping)
        results.append((group_name, result))
        
        # 如果不匹配，找出实际映射
        if not result:
            find_actual_source_mapping(file_path, en_source_mapping)
    
    # 葡萄牙语组
    for i, expected_ids in enumerate(experiment_groups['pt']):
        group_name = f"葡萄牙语{chr(65+i)}组"
        file_path = f"结果题目/pt_{chr(65+i)}.json"
        result = verify_experiment_mapping(file_path, group_name, expected_ids, pt_source_mapping)
        results.append((group_name, result))
        
        # 如果不匹配，找出实际映射
        if not result:
            find_actual_source_mapping(file_path, pt_source_mapping)
    
    # 4. 最终总结
    print(f"\n=== 最终验证结果 ===")
    all_correct = True
    for group_name, result in results:
        status = "✅ 正确" if result else "❌ 不正确"
        print(f"{group_name}: {status}")
        if not result:
            all_correct = False
    
    if all_correct:
        print(f"\n🎉 所有结果题目都按照实验组ID从源题目正确获取！")
    else:
        print(f"\n⚠️ 部分结果题目的ID映射存在问题")

if __name__ == "__main__":
    main()
