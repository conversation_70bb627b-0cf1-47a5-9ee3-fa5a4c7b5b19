[{"answer": "Bethlehem", "ref": "Luke 2:4-7", "update_time": null, "id": "28", "level": "0", "question": "<PERSON> was born in ___.", "count": "4", "quiz_index": "1", "error": "[\"Bethlehem\",\"Jerusalem\",\"Tiberian\",\"Samaria\"]"}, {"answer": "Dust", "ref": "Genesis 1:1-31", "update_time": null, "id": "35", "level": "1", "question": "<PERSON> was created by <PERSON> with ___.", "count": "4", "quiz_index": "0", "error": "[\"Dust\",\"Water\",\"Air\",\"Stone\"]"}, {"answer": "<PERSON>", "ref": "1 Corinthians 15:45", "update_time": null, "id": "37", "level": "2", "question": "The first man was ___.", "count": "4", "quiz_index": "2", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "Garden of Eden", "ref": "Genesis 2:15", "update_time": null, "id": "9", "level": "3", "question": "<PERSON> and <PERSON> were created by <PERSON> and lived in the ___", "count": "4", "quiz_index": "5", "error": "[\"Noah\\u0027s Ark\",\"Canaan\",\"Garden of Eden\",\"A barren land\"]"}, {"answer": "Earth", "ref": "Genesis 1:1", "update_time": null, "id": "16", "level": "4", "question": "In the beginning God created the heaven and the ___.", "count": "4", "quiz_index": "6", "error": "[\"Earth\",\"Hell\",\"World\",\"Paradise\"]"}, {"answer": "3", "ref": "Proverbs 1:5", "update_time": null, "id": "32", "level": "5", "question": "<PERSON> was resurrected after ___ days.", "count": "4", "quiz_index": "7", "error": "[\"1\",\"3\",\"5\",\"10\"]"}, {"answer": "Mercy", "ref": "Psalms 145:8", "update_time": null, "id": "14", "level": "6", "question": "The LORD is gracious, And full of compassion; slow to anger, And of great ___.", "count": "4", "quiz_index": "8", "error": "[\"Anger\",\"Mercy\",\"Patience\",\"Power\"]"}, {"answer": "<PERSON>", "ref": "Genesis 6:13-14", "update_time": null, "id": "47", "level": "7", "question": "God told ___ to build an ark.", "count": "4", "quiz_index": "9", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "1 Corinthians 13:13", "update_time": null, "id": "12", "level": "8", "question": "The Apostle <PERSON> is also known as ___ <PERSON>.", "count": "4", "quiz_index": "13", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Light|Darkness", "ref": "Genesis 1:4", "update_time": null, "id": "22", "level": "9", "question": "And God saw the ___, that it was good: and God divided the light from the ___.", "count": "4", "quiz_index": "12", "error": "[\"Light|Dust\",\"Light|Darkness\",\"Sun|Mist\",\"Sun|Rain\"]"}, {"answer": "40", "ref": "Genesis 7:12", "update_time": null, "id": "42", "level": "10", "question": "It rained ___ days and nights when <PERSON> was on the ark", "count": "4", "quiz_index": "10", "error": "[\"7\",\"10\",\"40\",\"50\"]"}, {"answer": "A rainbow", "ref": "Genesis 9:11-17", "update_time": null, "id": "27", "level": "11", "question": "What signal did <PERSON> send to <PERSON> so that he would never destroy the earth again?", "count": "4", "quiz_index": "11", "error": "[\"A firework\",\"A moon\",\"A rainbow\",\"A star\"]"}, {"answer": "Delilah", "ref": "1 <PERSON> 3:20", "update_time": null, "id": "4", "level": "12", "question": "Who caused <PERSON> to lose his Power?", "count": "4", "quiz_index": "16", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "Rest", "ref": "Matthew 11:28", "update_time": null, "id": "11", "level": "13", "question": "Come unto me, All ye that labour and are heavy laden, And I will give you ___.", "count": "4", "quiz_index": "14", "error": "[\"Faith\",\"Mercy\",\"Love\",\"Rest\"]"}, {"answer": "Two", "ref": "Exodus 7:14-12:36", "update_time": null, "id": "49", "level": "14", "question": "How many dogs entered the ark?", "count": "4", "quiz_index": "17", "error": "[\"Two\",\"One\",\"Three\",\"Ten\"]"}, {"answer": "<PERSON>", "ref": "Luke 2:5-7", "update_time": null, "id": "21", "level": "15", "question": "<PERSON>' human mother was ___.", "count": "4", "quiz_index": "15", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Deuteronomy 28:23", "update_time": null, "id": "23", "level": "16", "question": " ___ was called 'blessed among women'", "count": "4", "quiz_index": "19", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "12", "ref": "Luke 6:13", "update_time": null, "id": "7", "level": "17", "question": "<PERSON> chose ___ disciples.", "count": "4", "quiz_index": "21", "error": "[\"10\",\"12\",\"15\",\"20\"]"}, {"answer": "Blessed", "ref": "Proverbs 10:7", "update_time": null, "id": "44", "level": "18", "question": "The memory of the just is ___: but the name of the wicked shall rot.", "count": "4", "quiz_index": "20", "error": "[\"Blessed\",\"Revered\",\"Loved\",\"Spurned\"]"}, {"answer": "Peace｜Men", "ref": "Luke 2:14", "update_time": null, "id": "19", "level": "19", "question": "Glory to God in the highest, And on earth ___, Good will toward ___.", "count": "4", "quiz_index": "18", "error": "[\"Faith｜Men\",\"Grace｜Women\",\"Peace｜Men\",\"Power｜People\"]"}, {"answer": "Loaves and fishes", "ref": "Matthew 14:19", "update_time": null, "id": "46", "level": "20", "question": "What did <PERSON> fed the multitude with as part of his miracle？", "count": "4", "quiz_index": "25", "error": "[\"A calf\",\"Bread\",\"Loaves and fishes\",\"Pizza\"]"}, {"answer": "Sheep", "ref": "Genesis 21:27", "update_time": null, "id": "36", "level": "21", "question": "And <PERSON> took ___ and oxen, and gave them unto Abimelech; and both of them made a covenant.", "count": "4", "quiz_index": "26", "error": "[\"Camel\",\"Chick\",\"<PERSON>\",\"Sheep\"]"}, {"answer": "Angels", "ref": "Hebrews 13:2", "update_time": null, "id": "43", "level": "22", "question": "Be not forgetful to entertain strangers: for thereby some have entertained ___ unawares.", "count": "4", "quiz_index": "24", "error": "[\"Believers\",\"Ministers\",\"Angels\",\"<PERSON>in\"]"}, {"answer": "<PERSON>", "ref": "Luke 22:61", "update_time": null, "id": "40", "level": "23", "question": "___ denied <PERSON> three times.", "count": "4", "quiz_index": "22", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Matthew 13:55", "update_time": null, "id": "5", "level": "24", "question": "What was <PERSON>' job before he began his ministry?", "count": "4", "quiz_index": "23", "error": "[\"<PERSON>\",\"<PERSON>er\",\"<PERSON>\",\"Teacher\"]"}, {"answer": "<PERSON>", "ref": "Exodus 20:1-26", "update_time": null, "id": "26", "level": "25", "question": "Who received the 10 Commandments from <PERSON>?", "count": "4", "quiz_index": "29", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "God", "ref": "<PERSON> 6:3", "update_time": null, "id": "24", "level": "26", "question": "In the beginning was the Word, And the Word was with God, And the Word was ___.", "count": "4", "quiz_index": "28", "error": "[\"Love\",\"Faith\",\"Holy\",\"God\"]"}, {"answer": "The unrighteous", "ref": "1 Corinthians 6:9", "update_time": null, "id": "30", "level": "27", "question": "Know ye not that the unrighteous shall not inherit the kingdom of God?", "count": "4", "quiz_index": "31", "error": "[\"Adulterers\",\"Fornicators\",\"Idolaters\",\"The unrighteous\"]"}, {"answer": "Didn't believe it", "ref": "Mark 16:13", "update_time": null, "id": "50", "level": "28", "question": "How did <PERSON>' disciples react when they learned that <PERSON> had risen?", "count": "4", "quiz_index": "30", "error": "[\"Didn\\u0027t believe it\",\"Didn\\u0027t care it\",\"Were ecstatic\",\"Ran away\"]"}, {"answer": "Sunday", "ref": "Mark 16:9", "update_time": null, "id": "33", "level": "29", "question": "On what day of the week was <PERSON> resurrected?", "count": "4", "quiz_index": "27", "error": "[\"Friday\",\"Thursday\",\"Monday\",\"Sunday\"]"}, {"answer": "3 John", "ref": "3 <PERSON> 1:1-14", "update_time": null, "id": "10", "level": "30", "question": "The shortest book of the Bible is __.", "count": "4", "quiz_index": "33", "error": "[\"3 <PERSON>\",\"Romans\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Genesis 35:22", "update_time": null, "id": "6", "level": "31", "question": " ___ said, 'My Father is greater than I'.", "count": "4", "quiz_index": "34", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Hear", "ref": "<PERSON> 1:1-10:3", "update_time": null, "id": "8", "level": "32", "question": "And he said unto them, He that hath ears to hear, let him ___.", "count": "4", "quiz_index": "35", "error": "[\"Look\",\"Hear\",\"Speak\",\"Read\"]"}, {"answer": "<PERSON>", "ref": "Genesis 22:2", "update_time": null, "id": "18", "level": "33", "question": "Who did God ask <PERSON> to sacrifice to prove his loyalty to him？", "count": "4", "quiz_index": "36", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "40", "ref": "Matthew 4:2", "update_time": null, "id": "31", "level": "34", "question": "How many days did <PERSON> fast in the wilderness？", "count": "4", "quiz_index": "32", "error": "[\"10\",\"22\",\"40\",\"60\"]"}, {"answer": "Eyes to see", "ref": "Deuteronomy 29:4", "update_time": null, "id": "39", "level": "35", "question": " Yet the LORD hath not given you an heart to perceive, and ___, and ears to hear, unto this day.", "count": "4", "quiz_index": "43", "error": "[\"Brain to think\",\"Eyes to see\",\"Foot to walk\",\"Nose to smell\"]"}, {"answer": "Generosity", "ref": "Galatians 5:22", "update_time": null, "id": "17", "level": "36", "question": "The fruit of the Spirit is Love, Joy, Peace, Patience, Kindness, ___, Faithfulness, Gentleness, And self-control.", "count": "4", "quiz_index": "42", "error": "[\"Hope\",\"Calm\",\"faith\",\"Generosity\"]"}, {"answer": "Great riches", "ref": "Proverbs 22:1", "update_time": null, "id": "34", "level": "37", "question": "Having a good name or finding loving favour is better than ___ riches and silver and gold.", "count": "4", "quiz_index": "38", "error": "[\"Infinite life\",\"Great riches\",\"Countless servants\",\"Obedient child\"]"}, {"answer": "The Holy Spirit", "ref": "Proverbs 19:15", "update_time": null, "id": "51", "level": "38", "question": "<PERSON> was conceived by ___.", "count": "4", "quiz_index": "37", "error": "[\"<PERSON><PERSON>\",\"The Holy Spirit\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Eve", "ref": "Genesis 3:20", "update_time": null, "id": "38", "level": "39", "question": "Who is <PERSON>'s wife?", "count": "4", "quiz_index": "39", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Bless", "ref": "Luke 6:28", "update_time": null, "id": "2", "level": "40", "question": "___ them that curse you, And pray for them which despitefully use you.'", "count": "4", "quiz_index": "48", "error": "[\"Honor\",\"Bless\",\"Love\",\"Praise\"]"}, {"answer": "Trust|Understanding", "ref": "Proverbs 3:5", "update_time": null, "id": "41", "level": "41", "question": "___ in the Lord with all thine heart, And lean not unto thine own ___.", "count": "4", "quiz_index": "44", "error": "[\"Trust|Justice\",\"Believe|Cognitione\",\"Trust|Understanding\",\"Love|Intuition\"]"}, {"answer": "God", "ref": "Matthew 3:16", "update_time": null, "id": "48", "level": "42", "question": "When <PERSON> came out of the water, whose voice did he hear?", "count": "4", "quiz_index": "47", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON> the Baptist", "ref": "Matthew 3:13", "update_time": null, "id": "1", "level": "43", "question": "Who baptized Jesus?", "count": "4", "quiz_index": "45", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON> the Baptist\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Amen", "ref": "Revelation 22:21", "update_time": null, "id": "25", "level": "44", "question": "The last word of the Bible is ___.", "count": "4", "quiz_index": "46", "error": "[\"Amen\",\"Love\",\"We\",\"<PERSON>\"]"}, {"answer": "Rock", "ref": "Isaiah 26:4", "update_time": null, "id": "29", "level": "45", "question": "According to Isaiah 26:4, the Lord <PERSON><PERSON><PERSON> is___.", "count": "4", "quiz_index": "51", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Rocking horse maker\"]"}, {"answer": "Eyes", "ref": "Genesis 31:40", "update_time": null, "id": "3", "level": "46", "question": "In the day the drought consumed me, and the frost by night; and my sleep departed from mine ___.", "count": "4", "quiz_index": "50", "error": "[\"<PERSON>\",\"<PERSON>\",\"Heart\",\"Body\"]"}, {"answer": "Heart", "ref": "Psalms 51:10", "update_time": null, "id": "15", "level": "47", "question": "Create in me a clean ___, O <PERSON>; and renew a right spirit within me.", "count": "4", "quiz_index": "49", "error": "[\"Body\",\"Heart\",\"Mind\",\"Spirit\"]"}, {"answer": "<PERSON>", "ref": "Acts 4:12", "update_time": null, "id": "20", "level": "48", "question": "According to Acts 4:12, who can save your soul?", "count": "4", "quiz_index": "53", "error": "[\"<PERSON>\",\"Your mother\",\"Nobody\",\"Two others\"]"}, {"answer": "Wine", "ref": "Job 3:3", "update_time": null, "id": "13", "level": "49", "question": "'___ stands for Body of Jesus in the Lord's supper.", "count": "4", "quiz_index": "52", "error": "[\"Wine\",\"Water\",\"Milk\",\"Beer\"]"}, {"answer": "God", "ref": "<PERSON> 1:1", "update_time": null, "id": "45", "level": "50", "question": "In the beginning was the Word, And the Word was with ___, And the Word was God.", "count": "4", "quiz_index": "54", "error": "[\"Light\",\"God\",\"Love\",\"Holy\"]"}, {"answer": "30", "ref": "2 <PERSON> 5:4", "update_time": null, "id": "114", "level": "70", "question": "How old was <PERSON> when he became king?", "count": "4", "quiz_index": "77", "error": "[\"20\",\"25\",\"30\",\"40\"]"}, {"answer": "Red", "ref": "Matthew 2:8", "update_time": null, "id": "113", "level": "71", "question": "<PERSON> parted ___ sea.", "count": "4", "quiz_index": "84", "error": "[\"Arabah\",\"Black\",\"Red\",\"Mediterranean\"]"}, {"answer": "Gold Calf", "ref": "Exodus 32:15-35", "update_time": null, "id": "116", "level": "72", "question": "When <PERSON> returned from Mount Sinai with 10 commandments, the ___ was a false idol of the Israelites", "count": "4", "quiz_index": "75", "error": "[\"Gold Calf\",\"Gold Lamb\",\"Gold Horse\",\"Gold Dog\"]"}, {"answer": "<PERSON>'s family", "ref": "1 Kings 14:2", "update_time": null, "id": "115", "level": "73", "question": "___ was saved from the flood.", "count": "4", "quiz_index": "80", "error": "[\"<PERSON>\\u0027s family\",\"<PERSON>\\u0027s family\",\"<PERSON><PERSON>\\u0027s family\",\"Noah\\u0027s family\"]"}, {"answer": "Passover", "ref": "Mark 14:16", "update_time": null, "id": "118", "level": "74", "question": "What feast the jews were celebrating when jesus was crucified?", "count": "4", "quiz_index": "79", "error": "[\"Atonement\",\"Sabbath\",\"Passover\",\"Tabernacles\"]"}, {"answer": "Fell sleep", "ref": "Luke 22:45", "update_time": null, "id": "117", "level": "75", "question": "While <PERSON> suffered the Atonement, <PERSON>, <PERSON>, and <PERSON> ___. ", "count": "4", "quiz_index": "82", "error": "[\"Fell sleep\",\"Looked for betrayer\",\"Were indifferent\",\"Ran away\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Acts 12:23", "update_time": null, "id": "120", "level": "76", "question": "___ was 'eaten of worms' and eventually died.", "count": "4", "quiz_index": "83", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Matthew 1:17", "update_time": null, "id": "119", "level": "77", "question": "Who parted Red sea？", "count": "4", "quiz_index": "76", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "The synagogue", "ref": "Mark 3:1-5", "update_time": null, "id": "122", "level": "78", "question": "Where did <PERSON> heal a man with the withered hand?", "count": "4", "quiz_index": "81", "error": "[\"The Market\",\"The Street\",\"The Temple\",\"The synagogue\"]"}, {"answer": "Because <PERSON> Didn't wash hands", "ref": "<PERSON> 11:38", "update_time": null, "id": "121", "level": "79", "question": "Why were the Pharisees surprised when <PERSON> had dinner at their home？", "count": "4", "quiz_index": "78", "error": "[\"Because <PERSON> Ate beef\",\"Because <PERSON> Didn\\u0027t eat\",\"Because <PERSON> Didn\\u0027t wash hands\",\"Because <PERSON> Didn\\u0027t sit\"]"}, {"answer": "Serpent", "ref": "Luke 3:23", "update_time": null, "id": "124", "level": "80", "question": "<PERSON>'s rod became a___.", "count": "4", "quiz_index": "87", "error": "[\"Serpent\",\"Sword\",\"Saber\",\"Pair of wings\"]"}, {"answer": "Lot", "ref": "Genesis 12:5", "update_time": null, "id": "123", "level": "110", "question": "<PERSON> raised his <PERSON><PERSON><PERSON>w ___.", "count": "4", "quiz_index": "116", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "His head", "ref": "Psalms 23:5", "update_time": null, "id": "126", "level": "111", "question": "Where the lord anointed with oil", "count": "4", "quiz_index": "126", "error": "[\"His food\",\"His hands\",\"His head\",\"His foot\"]"}, {"answer": "Mouth|Heart", "ref": "Isaiah 29:13", "update_time": null, "id": "125", "level": "112", "question": "This people draw near Me with their ___, And ...,but have removed their ___ far from me,", "count": "4", "quiz_index": "122", "error": "[\"Lips|Heart\",\"Hands|Feet\",\"Heart|Minds\",\"Mouth|Heart\"]"}, {"answer": "1000", "ref": "Judges 15:16", "update_time": null, "id": "128", "level": "113", "question": "Samson killed ___ <PERSON><PERSON><PERSON> with a donkey's jawbone.", "count": "4", "quiz_index": "124", "error": "[\"1000\",\"200\",\"500\",\"800\"]"}, {"answer": "<PERSON>", "ref": "<PERSON> 7:15", "update_time": null, "id": "127", "level": "114", "question": "From which book does 'Wolf in sheep's clothing'come from?", "count": "4", "quiz_index": "125", "error": "[\"Ephesians\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "The sixth hour", "ref": "<PERSON> 4:6", "update_time": null, "id": "130", "level": "115", "question": "<PERSON> therefore, being wearied with his journey, sat thus on the well: and it was about ___.", "count": "4", "quiz_index": "106", "error": "[\"The seventh hour\",\"The sixth hour\",\"The sixth minute\",\"The seventh second\"]"}, {"answer": "Health", "ref": "<PERSON> 30:17", "update_time": null, "id": "129", "level": "116", "question": "For I will restore ___ unto thee, and I will heal thee of thy wounds, saith the LORD.", "count": "4", "quiz_index": "111", "error": "[\"Health\",\"Happy\",\"Pure\",\"Faithful\"]"}, {"answer": "A centurion", "ref": "Matthew 8:8", "update_time": null, "id": "132", "level": "117", "question": "___ said: 'Lord, I am not worthy: speak the word only, And my servant shall be healed'.", "count": "4", "quiz_index": "112", "error": "[\"A centurion\",\"A widow of <PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Nicodemus\"]"}, {"answer": "<PERSON>", "ref": "Revelation 22:16", "update_time": null, "id": "131", "level": "118", "question": "Who was called 'The Bright and Morning Star'.", "count": "4", "quiz_index": "115", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Ecclesiastes", "ref": "Ecclesiastes 3:1", "update_time": null, "id": "134", "level": "119", "question": " ___ mentions'To every thing there is a season, and a time to every purpose under the heaven'.", "count": "4", "quiz_index": "119", "error": "[\"Ecclesiastes\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Ezekiel\"]"}, {"answer": "Bethlehem", "ref": "1 <PERSON> 16:1-13", "update_time": null, "id": "133", "level": "120", "question": "Where was <PERSON> anointed as king of Israel？", "count": "4", "quiz_index": "127", "error": "[\"Samaria\",\"Bethel\",\"Bethlehem\",\"Nazareth\"]"}, {"answer": "Hypocrisy", "ref": "Luke 12:1", "update_time": null, "id": "136", "level": "121", "question": "The leaven of the pharisees is ___.", "count": "4", "quiz_index": "149", "error": "[\"Bitterness\",\"Hypocrisy\",\"Faith\",\"Vanity\"]"}, {"answer": "The tree of life", "ref": "Revelation 22:2", "update_time": null, "id": "135", "level": "122", "question": "A river flows down the middle of the street.The trees on both sides of it is named ___.", "count": "4", "quiz_index": "147", "error": "[\"The tree of love\",\"The tree of life\",\"The tree of hope\",\"The tree of purity\"]"}, {"answer": "<PERSON>", "ref": "1 Chronicles 22:7", "update_time": null, "id": "138", "level": "123", "question": "Who first desired to build a temple for the Lord.", "count": "4", "quiz_index": "140", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Branch|Withered", "ref": "<PERSON> 15:6", "update_time": null, "id": "137", "level": "124", "question": "If a man abide not in me, he is cast forth as a ___, and is ___;", "count": "4", "quiz_index": "157", "error": "[\"Branch|Withered\",\"<PERSON>|Burned\",\"<PERSON>|Withered\",\"Twig|Scorched\"]"}, {"answer": "<PERSON>", "ref": "Judges 3:21-22", "update_time": null, "id": "140", "level": "125", "question": "<PERSON> was murdered by ___.", "count": "4", "quiz_index": "135", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Exodus 2:15", "update_time": null, "id": "139", "level": "126", "question": "Where did <PERSON> flee after killing the Egyptian？", "count": "4", "quiz_index": "150", "error": "[\"<PERSON><PERSON>\",\"Midian\",\"The Red Sea\",\"Mount NeBo\"]"}, {"answer": "Serpents", "ref": "Exodus 7:1-11:10", "update_time": null, "id": "142", "level": "127", "question": "Ten Plagues of Egypt did not include___.", "count": "4", "quiz_index": "128", "error": "[\"Locusts\",\"Boils\",\"<PERSON>ce\",\"Serpents\"]"}, {"answer": "<PERSON>", "ref": "Acts 22:25", "update_time": null, "id": "141", "level": "128", "question": "Who was a Roman citizen among twelve apostles？", "count": "4", "quiz_index": "145", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Fire and brimstone", "ref": "Genesis 19:24", "update_time": null, "id": "144", "level": "129", "question": "The Lord destroyed Sodom and Gomorrah with ___.", "count": "4", "quiz_index": "155", "error": "[\"A flood\",\"An army\",\"An earthquake\",\"Fire and brimstone\"]"}, {"answer": "Jesus Christ", "ref": "Acts 22:21", "update_time": null, "id": "143", "level": "130", "question": "___ directed <PERSON> to preach to the Gentiles.", "count": "4", "quiz_index": "142", "error": "[\"<PERSON> Christ\",\"<PERSON> the Baptist\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Leviticus 4:2-3", "update_time": null, "id": "52", "level": "150", "question": "At the time of <PERSON>, what did a person have to sacrifice to receive forgiveness if he sinned in ignorance?", "count": "4", "quiz_index": "181", "error": "[\"<PERSON><PERSON>\",\"<PERSON>rrow\",\"Goa<PERSON>\",\"Pigeon\"]"}, {"answer": "Zipporah", "ref": "Exodus 2:21", "update_time": null, "id": "99", "level": "151", "question": "___ married <PERSON>.", "count": "4", "quiz_index": "158", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Zipporah\"]"}, {"answer": "Jesus Christ", "ref": "Judges 15:16", "update_time": null, "id": "62", "level": "152", "question": "___ is the one mediator between <PERSON> and men.", "count": "4", "quiz_index": "167", "error": "[\"<PERSON>\",\"<PERSON> Mary\",\"Jesus Christ\",\"<PERSON>\"]"}, {"answer": "Psalms", "ref": "Psalms 1:1-150:6", "update_time": null, "id": "64", "level": "153", "question": "Which book is the longest in the Old Testament?", "count": "4", "quiz_index": "191", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Mark 10:46-52", "update_time": null, "id": "96", "level": "154", "question": "What was the name of the blind man who asked <PERSON> to restore his sight in Jericho?", "count": "4", "quiz_index": "196", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Jachin and Boaz", "ref": "1 Kings 7:21", "update_time": null, "id": "104", "level": "155", "question": "The two pillars in the porch of the temple were ___.", "count": "4", "quiz_index": "175", "error": "[\"<PERSON><PERSON><PERSON> and <PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON> a <PERSON>\",\"<PERSON><PERSON><PERSON> and <PERSON><PERSON>\"]"}, {"answer": "<PERSON> and <PERSON>", "ref": "Luke 16:19-31", "update_time": null, "id": "81", "level": "156", "question": "The Parable of the ___ tells the lesson of indifference to the suffering of the poor is a sin.", "count": "4", "quiz_index": "192", "error": "[\"Good Samaritan\",\"Rich Fool\",\"Rich Man and Lazarus\",\"Unforgiving Servant\"]"}, {"answer": "Caught up to <PERSON>", "ref": "Revelation 12:1-5", "update_time": null, "id": "65", "level": "157", "question": "In Revelation 12, after the male child was born to the woman clothed with the sun, he was ___.", "count": "4", "quiz_index": "194", "error": "[\"Caught up to <PERSON>\",\"Left in the desert\",\"<PERSON><PERSON>\",\"Swallowed up\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Acts 16:21", "update_time": null, "id": "61", "level": "158", "question": "Where did some men accuse <PERSON> and <PERSON>, saying that they 'teach customs, which are not lawful for us'?", "count": "4", "quiz_index": "195", "error": "[\"Athens\",\"Derbe\",\"Lystra\",\"Philippi\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Ezra 4:3", "update_time": null, "id": "108", "level": "159", "question": "Which of these did not oppose the rebuilding the walls of Jerusalem?", "count": "4", "quiz_index": "161", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Tobia<PERSON>\",\"Zerubbabel\"]"}, {"answer": "Cave of Machpelah", "ref": "Genesis 23:19", "update_time": null, "id": "69", "level": "160", "question": "<PERSON> buried <PERSON> in the ___.", "count": "4", "quiz_index": "174", "error": "[\"Beersheba\",\"Cave of Machpelah\",\"Field near Bethlehem\",\"Jerusalem\"]"}, {"answer": "11", "ref": "1 Thessalonians 4:16", "update_time": null, "id": "94", "level": "161", "question": "<PERSON> had ___ brothers.", "count": "4", "quiz_index": "159", "error": "[\"10\",\"11\",\"12\",\"13\"]"}, {"answer": "<PERSON>", "ref": "Genesis 15:18-21", "update_time": null, "id": "74", "level": "162", "question": "___ was the ancestor of both the Jews and the Arabs.", "count": "4", "quiz_index": "182", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Exodus", "ref": "Exodus 7:14-12:36", "update_time": null, "id": "102", "level": "163", "question": "The plagues in which book of the Bible?", "count": "4", "quiz_index": "171", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Numbers\"]"}, {"answer": "8", "ref": "1 <PERSON> 21:13", "update_time": null, "id": "55", "level": "164", "question": "___ people were saved on the ark.", "count": "4", "quiz_index": "190", "error": "[\"14\",\"10\",\"8\",\"6\"]"}, {"answer": "Holdeth his peace", "ref": "Proverbs 11:12", "update_time": null, "id": "54", "level": "165", "question": "He that is void of wisdom despiseth his neighbor but a man of understanding ___.", "count": "4", "quiz_index": "198", "error": "[\"Envyth others\",\"Helpth his neighbor\",\"Holdeth his peace\",\"Looketh to do harm\"]"}, {"answer": "Rod and reproof", "ref": "Proverbs 29:15", "update_time": null, "id": "91", "level": "166", "question": "The ___ give wisdom: but a child left to himself bringeth his mother to shame.", "count": "4", "quiz_index": "164", "error": "[\"Barrier and lesson\",\"Endeavor and rod\",\"Obstacle and help\",\"Rod and reproof\"]"}, {"answer": "<PERSON>", "ref": "Genesis 31:19", "update_time": null, "id": "59", "level": "167", "question": "___ was the first person <PERSON> visited after his resurrection.", "count": "4", "quiz_index": "163", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Evilmerodach", "ref": "<PERSON> 52:31", "update_time": null, "id": "103", "level": "168", "question": "What was the name of the king who set <PERSON><PERSON><PERSON><PERSON> free from prison?", "count": "4", "quiz_index": "172", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"Evilmerodach\",\"Nebuchadnazzar\"]"}, {"answer": "Forever", "ref": "Isaiah 32:17", "update_time": null, "id": "70", "level": "169", "question": "According to Isaiah 32:17 what do you need to do to bring peace and confidence?", "count": "4", "quiz_index": "197", "error": "[\"For about 5 minutes\",\"Forever\",\"When you expect it\",\"You\\u0027re a good kid\"]"}, {"answer": "<PERSON>", "ref": "Genesis 37:22", "update_time": null, "id": "83", "level": "170", "question": "___ is the only book of the Bible that doesn't contain the word \"God\".", "count": "4", "quiz_index": "178", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>sal<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Acts 2:14-41", "update_time": null, "id": "56", "level": "171", "question": "Who preached the salvation message in Acts chapter two?", "count": "4", "quiz_index": "177", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Hunger", "ref": "1 <PERSON> 25:1", "update_time": null, "id": "97", "level": "172", "question": "Slothfulness casteth into a deep sleep; and an idle soul shall suffer ___.", "count": "4", "quiz_index": "162", "error": "[\"Insomnia\",\"Homeless\",\"Hunger\",\"Loneliness\"]"}, {"answer": "<PERSON>", "ref": "<PERSON> 12:3", "update_time": null, "id": "92", "level": "173", "question": "___ anointed <PERSON> with spikenard and then wiped his feet with her hair.", "count": "4", "quiz_index": "176", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "Moon and stars", "ref": "Genesis 1:14-19", "update_time": null, "id": "77", "level": "174", "question": "On the 4th day of creation, God made ___.", "count": "4", "quiz_index": "193", "error": "[\"Animals\",\"Moon and stars\",\"Plants\",\"Sky\"]"}, {"answer": "150", "ref": "Matthew 12:24", "update_time": null, "id": "87", "level": "175", "question": "The water covered the entire earth for ___days.", "count": "4", "quiz_index": "169", "error": "[\"150\",\"200\",\"100\",\"50\"]"}, {"answer": "Iron|Friend", "ref": "Proverbs 27:17", "update_time": null, "id": "79", "level": "176", "question": "Iron sharpeneth ___; so a man sharpeneth the countenance of his ___.", "count": "4", "quiz_index": "188", "error": "[\"<PERSON>|Father\",\"<PERSON>|Friend\",\"<PERSON>|Wife\",\"<PERSON>|Face\"]"}, {"answer": "Terrible famine", "ref": "Lamentations 4:8", "update_time": null, "id": "63", "level": "177", "question": "During Jerusalem's destruction, What caused the people's skin to turn black?", "count": "4", "quiz_index": "189", "error": "[\"Captive servitude\",\"Embracing dunghills\",\"Polluted blood\",\"Terrible famine\"]"}, {"answer": "Job", "ref": "2 Corinthians 12:4", "update_time": null, "id": "106", "level": "178", "question": "'Let the day perish wherein I was born'is mentioned in the book of___", "count": "4", "quiz_index": "165", "error": "[\"<PERSON>\",\"Hebrew\",\"<PERSON>\",\"Psalms\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Numbers 22:28", "update_time": null, "id": "88", "level": "179", "question": "___ was the King when <PERSON> was born.", "count": "4", "quiz_index": "183", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Land of Nod", "ref": "Genesis 4:16", "update_time": null, "id": "110", "level": "180", "question": "Where did <PERSON> go after he had killed <PERSON>?", "count": "4", "quiz_index": "184", "error": "[\"Caves of Qumran\",\"Garden of Eden\",\"Land of Nod\",\"Mount Ararat\"]"}, {"answer": "Lamentations", "ref": "Lamentations 1:1-5:22", "update_time": null, "id": "67", "level": "181", "question": "Which of these books has only 5 chapters?", "count": "4", "quiz_index": "185", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Lam<PERSON><PERSON>\",\"Song of Solomon\"]"}, {"answer": "Sixth", "ref": "Genesis 1:27-31", "update_time": null, "id": "73", "level": "182", "question": "In the book of Genesis, it says: 'God created man and animals on the ___ day.'", "count": "4", "quiz_index": "168", "error": "[\"Fifth\",\"Second\",\"Sixth\",\"Third\"]"}, {"answer": "12", "ref": "Exodus 28:17-20", "update_time": null, "id": "89", "level": "183", "question": "How many stones were on the 'breastplate of judgment' worn by the High Priest?", "count": "4", "quiz_index": "179", "error": "[\"12\",\"3\",\"4\",\"7\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>", "ref": "Judges 11:1", "update_time": null, "id": "53", "level": "184", "question": "Who was the son of a prostitute?", "count": "4", "quiz_index": "166", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "10", "ref": "Acts 16:3", "update_time": null, "id": "105", "level": "185", "question": "God sent ___ plagues on Egypt.", "count": "4", "quiz_index": "186", "error": "[\"10\",\"12\",\"15\",\"22\"]"}, {"answer": "Corinth", "ref": "Acts 18:12-15", "update_time": null, "id": "60", "level": "186", "question": "<PERSON> gained a favorable judgment by <PERSON><PERSON><PERSON>, a Roman deputy, against the Jews in ___.", "count": "4", "quiz_index": "187", "error": "[\"Antioch\",\"Corinth\",\"Derbe\",\"Lystra\"]"}, {"answer": "Unto him his wife", "ref": "Matthew 1:24", "update_time": null, "id": "109", "level": "187", "question": "In Matthew 1:24, What did <PERSON> being raised from sleep took as the angel of the Lord instructed?", "count": "4", "quiz_index": "160", "error": "[\"Unto him his clothes\",\"Unto him his money\",\"Unto him his rod\",\"Unto him his wife\"]"}, {"answer": "Worship Him", "ref": "Proverbs 10:22", "update_time": null, "id": "112", "level": "188", "question": "<PERSON><PERSON> told the Wise Men that once found the young child, let him have news of it, so that he could ___.", "count": "4", "quiz_index": "170", "error": "[\"Take care of Him\",\"Kill Him\",\"Honor Him\",\"Worship Him\"]"}, {"answer": "Ethiopian", "ref": "Numbers 12:1", "update_time": null, "id": "84", "level": "189", "question": "<PERSON>' brother and sister were upset with <PERSON> because he married ___.", "count": "4", "quiz_index": "180", "error": "[\"Ethiopian\",\"Hittite\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "God", "ref": "Habakkuk 1:13", "update_time": null, "id": "93", "level": "190", "question": "Your eyes are more pure than evil eyes, And you cannot see evil. What is the theme of this poem?", "count": "4", "quiz_index": "257", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Mahlon\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>", "ref": "Acts 5:1-10", "update_time": null, "id": "76", "level": "191", "question": "___ lied to the Holy Spirit and fell dead.", "count": "4", "quiz_index": "260", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"]"}, {"answer": "His older brother", "ref": "Genesis 48:18-19", "update_time": null, "id": "57", "level": "192", "question": "<PERSON> blessed <PERSON><PERSON><PERSON><PERSON> with his right hand because <PERSON><PERSON><PERSON><PERSON> would become a greater man than ___.", "count": "4", "quiz_index": "218", "error": "[\"His older brother\",\"His older sister\",\"His young brother\",\"<PERSON> himself\"]"}, {"answer": "Seven years", "ref": "Genesis 29:20", "update_time": null, "id": "95", "level": "193", "question": "Before being deceived, how long had <PERSON> planned to serve in order to marry <PERSON>?", "count": "4", "quiz_index": "250", "error": "[\"None of these\",\"Seven years\",\"Ten years\",\"Three years\"]"}, {"answer": "3", "ref": "Job 2:11", "update_time": null, "id": "72", "level": "194", "question": "How many of <PERSON>'s friends went to comfort him during his suffering?", "count": "4", "quiz_index": "214", "error": "[\"3\",\"4\",\"5\",\"6\"]"}, {"answer": "Cross", "ref": "Matthew 10:38", "update_time": null, "id": "82", "level": "195", "question": "And he that taketh not his ___, And followeth after me, Is not worthy of me.", "count": "4", "quiz_index": "210", "error": "[\"Bible\",\"Cross\",\"Family\",\"Life\"]"}, {"answer": "Belshazzar", "ref": "<PERSON> 5:9", "update_time": null, "id": "85", "level": "196", "question": "___ was greatly troubled by the writing on the wall.", "count": "4", "quiz_index": "234", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "4", "ref": "Exodus 2:3", "update_time": null, "id": "101", "level": "197", "question": "<PERSON> had ___ brothers.", "count": "4", "quiz_index": "252", "error": "[\"2\",\"3\",\"4\",\"5\"]"}, {"answer": "Macedonia", "ref": "Acts 16:9", "update_time": null, "id": "86", "level": "198", "question": "And a vision appeared to <PERSON> in the night; there stood a man of ___, And prayed him.", "count": "4", "quiz_index": "206", "error": "[\"Asia\",\"Bithynia\",\"Macedonia\",\"Phrygia\"]"}, {"answer": "Gospel", "ref": "Galatians 1:8", "update_time": null, "id": "107", "level": "199", "question": "The churches of Galatia was hearing another ___.", "count": "4", "quiz_index": "254", "error": "[\"Book\",\"Gospel\",\"Story\",\"Word\"]"}, {"answer": "Worship the statue", "ref": "Daniel 3:13-14", "update_time": null, "id": "80", "level": "200", "question": "King <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> became angry at <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, Because they refuse to ___.", "count": "4", "quiz_index": "211", "error": "[\"Interpret his dream\",\"Preach\",\"Worship the statue\",\"Worship to God\"]"}, {"answer": "Wash it", "ref": "Matthew 6:17", "update_time": null, "id": "68", "level": "201", "question": "What should we do to our face when we fast?", "count": "4", "quiz_index": "233", "error": "[\"Cover it\",\"Don\\u0027t touch it\",\"Put ashes on it\",\"Wash it\"]"}, {"answer": "<PERSON><PERSON>", "ref": "2 Kings 2:11-12", "update_time": null, "id": "100", "level": "202", "question": "Who witnessed <PERSON> being taken up to Heaven?", "count": "4", "quiz_index": "238", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Husband and wife", "ref": "Acts 5:1", "update_time": null, "id": "78", "level": "203", "question": "The relation between <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> was ___.", "count": "4", "quiz_index": "222", "error": "[\"Brother and sister\",\"Husband and wife\",\"Neighbors\",\"<PERSON>s\"]"}, {"answer": "666", "ref": "Revelation 13:18", "update_time": null, "id": "71", "level": "204", "question": "In Revelation, what's the number of a man?", "count": "4", "quiz_index": "256", "error": "[\"153\",\"213\",\"42\",\"666\"]"}, {"answer": "Stone", "ref": "Isaiah 8:14", "update_time": null, "id": "111", "level": "205", "question": "In Isaiah chapter 8, <PERSON> is described as a ___ of stumbling to the House of Israel.", "count": "4", "quiz_index": "253", "error": "[\"Block\",\"Pit\",\"Stone\",\"Trap\"]"}, {"answer": "<PERSON>", "ref": "Acts 19:24-41", "update_time": null, "id": "75", "level": "206", "question": "Who said to <PERSON>, 'Lord, we know not whither thou goest; and how can we know the way?'", "count": "4", "quiz_index": "204", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "All of the above", "ref": "Isaiah 9:6", "update_time": null, "id": "90", "level": "207", "question": "Isaiah 9:6 predicts that <PERSON> will be called the following name?", "count": "4", "quiz_index": "207", "error": "[\"All of the above\",\"Counselor\",\"Mighty God\",\"Prince of Peace\"]"}, {"answer": "The king's signet", "ref": "<PERSON> 6:17", "update_time": null, "id": "58", "level": "208", "question": "What was used to seal the den after <PERSON> was thrown in?", "count": "4", "quiz_index": "237", "error": "[\"Iron bars\",\"The King\\u0027s sword\",\"The king\\u0027s signet\",\"Wet clay\"]"}, {"answer": "<PERSON>", "ref": "1 Chronicles 3:1", "update_time": null, "id": "66", "level": "209", "question": "Who did <PERSON> appoint <PERSON> to succeed Israel as leader?", "count": "4", "quiz_index": "249", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Encouraging", "ref": "Luke 18:1-8", "update_time": null, "id": "98", "level": "210", "question": "The parable of the unjust judge who gave in to the wishes of the widow was intended to be ___.", "count": "4", "quiz_index": "241", "error": "[\"Admonishing\",\"Confusing\",\"Discouraging\",\"Encouraging\"]"}, {"answer": "<PERSON><PERSON>", "ref": "1 Kings 16:32", "update_time": null, "id": "146", "level": "250", "question": "In the days of <PERSON> the Canaanite God was still very influential. Which God?", "count": "4", "quiz_index": "261", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>gon\"]"}, {"answer": "A cave", "ref": "1 <PERSON> 24:4", "update_time": null, "id": "145", "level": "251", "question": "When cutting the skirt from <PERSON>'s robe, where were <PERSON> and his men hiding?", "count": "4", "quiz_index": "299", "error": "[\"A barn\",\"A cave\",\"A forest\",\"A well\"]"}, {"answer": "Transgression", "ref": "Proverbs 17:19", "update_time": null, "id": "148", "level": "252", "question": "He loveth ___ that loveth strife: and he that exalteth his gate seeketh destruction.", "count": "4", "quiz_index": "434", "error": "[\"Love\",\"Much gain\",\"Not God\",\"Transgression\"]"}, {"answer": "Hear", "ref": "Matthew 11:15", "update_time": null, "id": "147", "level": "253", "question": "He that hath ears to hear, Let him ___.", "count": "4", "quiz_index": "283", "error": "[\"Follow\",\"Hear\",\"Speak\",\"Teach\"]"}, {"answer": "<PERSON>", "ref": "Genesis 32:28", "update_time": null, "id": "150", "level": "254", "question": "Do you know whose name was changed to 'Israel'?", "count": "4", "quiz_index": "281", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Garden", "ref": "Genesis 2:8", "update_time": null, "id": "149", "level": "255", "question": "And the LORD God planted a ___ eastward in Eden; and there he put the man whom he had formed.", "count": "4", "quiz_index": "343", "error": "[\"Garden\",\"House\",\"Palace\",\"Temple\"]"}, {"answer": "Le<PERSON>", "ref": "Numbers 5:2", "update_time": null, "id": "152", "level": "256", "question": "<PERSON> told <PERSON> to remove ___ from his camp so as not to defile it.", "count": "4", "quiz_index": "295", "error": "[\"Alcoholics\",\"Lepers\",\"No one\",\"Prostitutes\"]"}, {"answer": "Heart|Broken spirit", "ref": "Proverbs 17:22", "update_time": null, "id": "151", "level": "257", "question": "A merry ___ doeth good like a medicine: but a ___ drieth the bones.", "count": "4", "quiz_index": "428", "error": "[\"Heart|Broken spirit\",\"Sleep|Troubled mind\",\"Song of mirth|Dirge\",\"Word|Reproof\"]"}, {"answer": "Crete", "ref": "Acts 27:7", "update_time": null, "id": "154", "level": "258", "question": "Where did the captain of the ship carrying <PERSON> to Rome wish to winter?", "count": "4", "quiz_index": "297", "error": "[\"Capri\",\"Crete\",\"Cyprus\",\"Jamaica\"]"}, {"answer": "Galatians 6:9", "ref": "Galatians 6:9", "update_time": null, "id": "153", "level": "259", "question": "If we do not lose heart, We will reap in the proper season. Where did it come from?", "count": "4", "quiz_index": "273", "error": "[\"Gala<PERSON><PERSON> 6:9\",\"<PERSON> 24:2\",\"<PERSON> 22:13\",\"<PERSON><PERSON><PERSON> 42:8\"]"}, {"answer": "<PERSON>", "ref": "1 Kings 17:1-6", "update_time": null, "id": "156", "level": "260", "question": "The ravens brought food to ___.", "count": "4", "quiz_index": "278", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Hatred|Love", "ref": "Proverbs 10:12", "update_time": null, "id": "155", "level": "261", "question": "___ stirreth up strifes: but ___ covereth all sins.", "count": "4", "quiz_index": "302", "error": "[\"Foolishness|Truth\",\"Hat<PERSON>|Love\",\"Malice|Patience\",\"<PERSON>|Wisdom\"]"}, {"answer": "Enemy|Glad", "ref": "2 <PERSON> 2:1-10", "update_time": null, "id": "158", "level": "262", "question": "Rejoice not when thine ___ falleth, And let not thine heart be ___ when he stumbleth.", "count": "4", "quiz_index": "270", "error": "[\"<PERSON><PERSON><PERSON><PERSON>|Glad\",\"<PERSON>|Sad\",\"Enemy|Glad\",\"Friend|Sad\"]"}, {"answer": "God|Jesus Christ", "ref": "<PERSON> 17:3", "update_time": null, "id": "157", "level": "263", "question": "that they might know thee the only true ___, And ___, Whom thou hast sent.", "count": "4", "quiz_index": "350", "error": "[\"God|Jesus Christ\",\"Lord|Thy Son\",\"Sacrifice|<PERSON>\",\"<PERSON>|The Son\"]"}, {"answer": "Grievous words", "ref": "Proverbs 15:1", "update_time": null, "id": "160", "level": "264", "question": "A soft answer turneth away wrath: but ___ stir up anger.", "count": "4", "quiz_index": "330", "error": "[\"Deceitful gossips\",\"Grievous words\",\"The lips of fools\",\"Thoughtless gibes\"]"}, {"answer": "New Jerusalem", "ref": "Revelation 21:12", "update_time": null, "id": "159", "level": "265", "question": "The names of all tribes were written on the gates of ___.", "count": "4", "quiz_index": "264", "error": "[\"New Jerusalem\",\"Solomon\\u0027s Porch\",\"The City of David\",\"The Tabernacle\"]"}, {"answer": "Heaven and earth", "ref": "1 <PERSON> 31:4", "update_time": null, "id": "162", "level": "266", "question": "___shall pass away, But my words shall not pass away.", "count": "4", "quiz_index": "289", "error": "[\"Heaven and earth\",\"Heaven and hell\",\"Mountains and sea\",\"Stones and sea\"]"}, {"answer": "A man", "ref": "Proverbs 17:12", "update_time": null, "id": "161", "level": "267", "question": "Leta bear robbed of her whelps meet ___, Rather than a fool in his folly.", "count": "4", "quiz_index": "301", "error": "[\"A lame camel\",\"A leaky bucket\",\"A man\",\"Angels of God\"]"}, {"answer": "Fall", "ref": "Proverbs 10:8", "update_time": null, "id": "164", "level": "268", "question": "The wise in heart will receive commandments: but a prating fool shall ___.", "count": "4", "quiz_index": "416", "error": "[\"Argue\",\"Fall\",\"Speak aiainst king\",\"Speak nonsense\"]"}, {"answer": "All of the above", "ref": "Proverbs 22:4", "update_time": null, "id": "163", "level": "269", "question": "By humility and the fear of the LORD are ___.", "count": "4", "quiz_index": "378", "error": "[\"All of the above\",\"Honour\",\"Life\",\"Riches\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Judges 2:11", "update_time": null, "id": "166", "level": "270", "question": "After <PERSON> died, the children of Israel did evil in the sight of the Lord, and served ___.", "count": "4", "quiz_index": "331", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>gli\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>'s wife", "ref": "Ezra 10:44", "update_time": null, "id": "165", "level": "271", "question": "Who disguised herself before going to Ahijah?", "count": "4", "quiz_index": "286", "error": "[\"<PERSON><PERSON>\\u0027wife\",\"<PERSON>\\u0027s wife\",\"<PERSON><PERSON>\\u0027s wife\",\"<PERSON><PERSON><PERSON><PERSON>\\u0027s wife\"]"}, {"answer": "The Heavens", "ref": "Psalms 19:1", "update_time": null, "id": "168", "level": "272", "question": "A Psalm of <PERSON>: The ___ declare the glory of God.", "count": "4", "quiz_index": "294", "error": "[\"Levi<PERSON>\",\"<PERSON>s\",\"Rocks\",\"The Heavens\"]"}, {"answer": "He fell before", "ref": "2 <PERSON> 4:4", "update_time": null, "id": "167", "level": "273", "question": "<PERSON>'s son <PERSON><PERSON><PERSON><PERSON><PERSON> was lame because ___.", "count": "4", "quiz_index": "277", "error": "[\"He fell before\",\"He has a disease\",\"He is weak\",\"His horse ran on him\"]"}, {"answer": "A dry morsel", "ref": "Proverbs 17:1", "update_time": null, "id": "170", "level": "274", "question": "Better is ___, And quietness therewith, Than an house full of sacrifices with strife.", "count": "4", "quiz_index": "366", "error": "[\"A dry morsel\",\"A house without roof\",\"A submissive wife\",\"Starvation\"]"}, {"answer": "2", "ref": "<PERSON> 5:6-7:2", "update_time": null, "id": "169", "level": "275", "question": "How many banquets did <PERSON> host?", "count": "4", "quiz_index": "275", "error": "[\"1\",\"2\",\"4\",\"5\"]"}, {"answer": "A rich man", "ref": "Matthew 19:24", "update_time": null, "id": "172", "level": "276", "question": "For it is easier for a camel to go through a needle's eye, Than for ___ to enter the kingdom of God.", "count": "4", "quiz_index": "440", "error": "[\"A rich man\",\"Any man\",\"The Pharisees\",\"The poor\"]"}, {"answer": "Water|Spirit|Fire", "ref": "Matthew 3:11", "update_time": null, "id": "171", "level": "277", "question": "I indeed baptize you with ___: but he that cometh after me...Shall baptize you with ___ and with ___.", "count": "4", "quiz_index": "265", "error": "[\"Fire|Water|Spirit\",\"Water|Fire|Air\",\"Water|Spirit|Fire\",\"Water|Water|Spirit\"]"}, {"answer": "2", "ref": "Joshua 2:4-6", "update_time": null, "id": "174", "level": "278", "question": "<PERSON><PERSON> hid ___ spies under the stalks of flax on the roof.", "count": "4", "quiz_index": "271", "error": "[\"2\",\"3\",\"4\",\"5\"]"}, {"answer": "He made it with spit", "ref": "<PERSON> 9:6", "update_time": null, "id": "173", "level": "279", "question": "Where did <PERSON> get the mud he put on the eyes of the blind man healed in John 9?", "count": "4", "quiz_index": "269", "error": "[\"He made it with spit\",\"In a puddle\",\"Outside a house\",\"The base of a well\"]"}, {"answer": "Heart|Wounds", "ref": "Psalms 147:3", "update_time": null, "id": "176", "level": "280", "question": "He healeth the broken in ___, And bindeth up their ___.", "count": "4", "quiz_index": "319", "error": "[\"Body|Skin\",\"Friendship|Sins\",\"Heart|Wounds\",\"Spirit|Soul\"]"}, {"answer": "Vagabond", "ref": "Genesis 4:14", "update_time": null, "id": "175", "level": "281", "question": "Because <PERSON> murdered <PERSON>, the Lord curses him to a life as a:", "count": "4", "quiz_index": "292", "error": "[\"<PERSON>ggar\",\"Her<PERSON>\",\"<PERSON><PERSON>\",\"Vagabond\"]"}, {"answer": "Stripes", "ref": "Proverbs 19:29", "update_time": null, "id": "178", "level": "282", "question": "Judgments are prepared for scorners, And ___ for the back of fools.", "count": "4", "quiz_index": "348", "error": "[\"Barriers\",\"B<PERSON>ises\",\"Burdens\",\"Stripes\"]"}, {"answer": "30 days", "ref": "<PERSON> 6:7", "update_time": null, "id": "177", "level": "283", "question": "King <PERSON> commanded his kingdom pray only to him for ___.", "count": "4", "quiz_index": "288", "error": "[\"20 days\",\"30 days\",\"50 days\",\"One year\"]"}, {"answer": "Vine", "ref": "John 15:1-5", "update_time": null, "id": "180", "level": "284", "question": "Christ called Himself the true ___ and His apostles the branches.", "count": "4", "quiz_index": "282", "error": "[\"<PERSON>\",\"Hope\",\"Tree\",\"Vine\"]"}, {"answer": "<PERSON>", "ref": "<PERSON> 9:59", "update_time": null, "id": "179", "level": "285", "question": "Who stole the household gods of her father？", "count": "4", "quiz_index": "266", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "God's name", "ref": "Exodus 20:7", "update_time": null, "id": "182", "level": "286", "question": "The Third Commandment says, 'Do not misuse ___.'", "count": "4", "quiz_index": "272", "error": "[\"God\\u0027s name\",\"God\\u0027s voice\",\"Lord\\u0027s name\",\"Lord\\u0027s voice\"]"}, {"answer": "Shall be praised", "ref": "Proverbs 31:30", "update_time": null, "id": "181", "level": "287", "question": "Favour is deceitful, And beauty is vain: but a woman that feareth the LORD, She ___.", "count": "4", "quiz_index": "296", "error": "[\"Is a joy and a crown\",\"Shall be blessed\",\"Shall be loved\",\"Shall be praised\"]"}, {"answer": "<PERSON>", "ref": "1 Corinthians 13:13", "update_time": null, "id": "184", "level": "288", "question": "___ said, 'And now these three remain: faith, Hope and love. But the greatest of these is love.'", "count": "4", "quiz_index": "403", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON>'s|Woman", "ref": "Proverbs 11:22", "update_time": null, "id": "183", "level": "289", "question": "A jewel of gold in a ___ snout, So is a fair ___ which is without discretion.", "count": "4", "quiz_index": "414", "error": "[\"Dog\\u0027s|Maiden\",\"Man\\u0027s|Wife\",\"Swine\\u0027s|Woman\",\"Woman\\u0027s|Child\"]"}, {"answer": "66", "ref": "Matthew 27:35", "update_time": null, "id": "186", "level": "290", "question": "How many books are there in the bible?", "count": "4", "quiz_index": "274", "error": "[\"33\",\"10\",\"66\",\"15\"]"}, {"answer": "<PERSON>", "ref": "Genesis 6:22", "update_time": null, "id": "185", "level": "291", "question": "Thus did ___; according to all that <PERSON> commanded him, so did he.", "count": "4", "quiz_index": "408", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Genesis 35:8", "update_time": null, "id": "188", "level": "292", "question": "<PERSON><PERSON><PERSON>'s nurse died and was buried under the oak below Bethel, What is her name?", "count": "4", "quiz_index": "298", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"]"}, {"answer": "Nazareth", "ref": "Luke 2:39-40", "update_time": null, "id": "187", "level": "293", "question": "<PERSON> mostly raised at which place as a child?", "count": "4", "quiz_index": "263", "error": "[\"Cyprus\",\"Egypt\",\"Nazareth\",\"Sinai\"]"}, {"answer": "Jordan River", "ref": "Matthew 3:5-6", "update_time": null, "id": "190", "level": "294", "question": "In which river did <PERSON> the Baptist baptize people?", "count": "4", "quiz_index": "279", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"Jordan River\",\"Phar<PERSON>\",\"Tigris\"]"}, {"answer": "Was conceived", "ref": "Luke 1:31", "update_time": null, "id": "189", "level": "295", "question": "<PERSON>' name decided by the angel <PERSON> before ___.", "count": "4", "quiz_index": "276", "error": "[\"He was circumcised\",\"<PERSON> went to Egypt\",\"Time began\",\"Was conceived\"]"}, {"answer": "930", "ref": "Genesis 5:5", "update_time": null, "id": "192", "level": "296", "question": "How old was <PERSON> when he died?", "count": "4", "quiz_index": "293", "error": "[\"111\",\"333\",\"666\",\"930\"]"}, {"answer": "3", "ref": "2 Corinthians 12:8", "update_time": null, "id": "191", "level": "297", "question": "How many times did <PERSON> ask <PERSON> to remove his 'thorn in the flesh?'", "count": "4", "quiz_index": "268", "error": "[\"1\",\"2\",\"3\",\"4\"]"}, {"answer": "Worship him", "ref": "Matthew 2:8", "update_time": null, "id": "194", "level": "298", "question": "Herod the King told the Wise Men to send word to him once they found the young child so he could ___.", "count": "4", "quiz_index": "284", "error": "[\"Bring him gifts\",\"Kill him\",\"Make him king\",\"Worship him\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Acts 12:23", "update_time": null, "id": "193", "level": "299", "question": "The angel of the Lord struck down ___ because he gave not <PERSON> the glory.", "count": "4", "quiz_index": "290", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Proverbs 6:6", "update_time": null, "id": "195", "level": "300", "question": "Go to the ant, <PERSON><PERSON> sluggard; consider her ways and be ___.", "count": "4", "quiz_index": "517", "error": "[\"Perfect\",\"Prudent\",\"<PERSON>\",\"<PERSON>\"]"}]