[{"answer": "<PERSON><PERSON>", "ref": "Genesis 1:1-31", "update_time": null, "id": "1", "level": "0", "question": "Adão foi criado por Deus com ___.", "count": "4", "quiz_index": "0", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>r\",\"Pedra\"]"}, {"answer": "Belém", "ref": "Luke 2:4-7", "update_time": null, "id": "2", "level": "1", "question": "<PERSON> nasceu em ___.", "count": "4", "quiz_index": "1", "error": "[\"Belém\",\"Jerusalém\",\"Tiberiano\",\"Samária\"]"}, {"answer": "Adão", "ref": "1 Corinthians 15:45", "update_time": null, "id": "3", "level": "2", "question": "O primeiro homem foi ___.", "count": "4", "quiz_index": "2", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"Moisés\",\"<PERSON><PERSON>r<PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON> <PERSON>", "ref": "Genesis 2:15", "update_time": null, "id": "4", "level": "3", "question": "Adão e Eva foram criados por Deus e viveram no ___", "count": "4", "quiz_index": "5", "error": "[\"Sarandi<PERSON>\",\"Cana<PERSON>\",\"Jardim do Eden\",\"Terra estéril\"]"}, {"answer": "3", "ref": "Proverbs 1:5", "update_time": null, "id": "5", "level": "4", "question": "<PERSON> ressuscitou após ___ dias.", "count": "4", "quiz_index": "7", "error": "[1,3,5,10]"}, {"answer": "Terra", "ref": "Genesis 1:1", "update_time": null, "id": "6", "level": "5", "question": "No princípio <PERSON> criou o céu e o ___.", "count": "4", "quiz_index": "6", "error": "[\"Terra\",\"Inferno\",\"Mundo\",\"Paraíso\"]"}, {"answer": "Misericórdia", "ref": "Psalms 145:8", "update_time": null, "id": "7", "level": "6", "question": "O Senhor é misericordioso e cheio de compaixão; tardio em irar-se e de grande ___.", "count": "4", "quiz_index": "8", "error": "[\"<PERSON><PERSON>\",\"Misericórdia\",\"Paciência\",\"Poder\"]"}, {"answer": "Noé", "ref": "Genesis 6:13-14", "update_time": null, "id": "8", "level": "7", "question": "Deus disse ___ para construir uma arca.", "count": "4", "quiz_index": "9", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "luz|Escuridão", "ref": "Genesis 1:4", "update_time": null, "id": "9", "level": "8", "question": "E Deus viu o ___, que era bom: e Deus separou a luz do ___.", "count": "4", "quiz_index": "12", "error": "[\"luz|<PERSON><PERSON>\",\"luz|Escuridão\",\"Sol|Névoa\",\"Sol|Chuva\"]"}, {"answer": "Simão", "ref": "1 Coríntios 13:13", "update_time": null, "id": "10", "level": "9", "question": "<PERSON> Após<PERSON>lo Pedro também é conhecido como ___ <PERSON>.", "count": "4", "quiz_index": "13", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "40", "ref": "Genesis 7:12", "update_time": null, "id": "11", "level": "10", "question": "Choveu ___ dias e noites quando Noé estava na arca", "count": "4", "quiz_index": "10", "error": "[\"7\",\"10\",\"40\",\"50\"]"}, {"answer": "<PERSON> arco-íris", "ref": "Genesis 9:11-17", "update_time": null, "id": "12", "level": "11", "question": "Que sinal Deus enviou a Noé para que ele nunca mais destruísse a terra?", "count": "4", "quiz_index": "11", "error": "[\"Um fogo de artifício\",\"Uma lua\",\"Um arco-íris\",\"Uma estrela\"]"}, {"answer": "Dal<PERSON>", "ref": "1 <PERSON> 3:20", "update_time": null, "id": "13", "level": "12", "question": "Quem fez com que Sansão perdesse seu poder?", "count": "4", "quiz_index": "16", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Exodus 7:14-12:36", "update_time": null, "id": "14", "level": "13", "question": "Quantos cachorros entraram na arca?", "count": "4", "quiz_index": "17", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "Descanso", "ref": "Matthew 11:28", "update_time": null, "id": "15", "level": "14", "question": "Vinde a mim, todos os que estais cansados ​​e sobrecarregados, e eu vos darei ___.", "count": "4", "quiz_index": "14", "error": "[\"<PERSON><PERSON>\",\"Misericórdia\",\"Amor\",\"Descanso\"]"}, {"answer": "<PERSON>", "ref": "Luke 2:5-7", "update_time": null, "id": "16", "level": "15", "question": "<PERSON>' mãe humana era ___.", "count": "4", "quiz_index": "15", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Deuteronomy 28:23", "update_time": null, "id": "17", "level": "16", "question": " ___ foi chamada de '<PERSON><PERSON><PERSON><PERSON><PERSON> entre as mulheres'", "count": "4", "quiz_index": "19", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Proverbs 10:7", "update_time": null, "id": "18", "level": "17", "question": "A memória do justo é ___: mas o nome dos ímpios apodrecerá.", "count": "4", "quiz_index": "20", "error": "[\"Abençoado\",\"Reverenciado\",\"Amavam\",\"Desprezado\"]"}, {"answer": "12", "ref": "Luke 6:13", "update_time": null, "id": "19", "level": "18", "question": "<PERSON> escolheu ___ disc<PERSON>pulos.", "count": "4", "quiz_index": "21", "error": "[\"10\",\"12\",\"15\",\"20\"]"}, {"answer": "Paz｜Homens", "ref": "Luke 2:14", "update_time": null, "id": "20", "level": "19", "question": "Glória a Deus nas alturas, E na terra ___, <PERSON><PERSON> para com ___.", "count": "4", "quiz_index": "18", "error": "[\"Faith｜Men\",\"Graça｜Mulheres\",\"Paz｜Homens\",\"Poder｜Pessoas\"]"}, {"answer": "Pães e peixes", "ref": "Matthew 14:19", "update_time": null, "id": "21", "level": "20", "question": "Com o que Jesus alimentou a multidão como parte de seu milagre?", "count": "4", "quiz_index": "25", "error": "[\"<PERSON> bezerro\",\"<PERSON><PERSON>\",\"Pães e peixes\",\"Pizza\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Hebrews 13:2", "update_time": null, "id": "22", "level": "21", "question": "Não se esqueça de entreter estranhos: pois assim alguns têm entretido ___ de surpresa.", "count": "4", "quiz_index": "24", "error": "[\"Crentes\",\"Ministros\",\"Anjos\",\"Vilão\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Genesis 21:27", "update_time": null, "id": "23", "level": "22", "question": "E tomou <PERSON> ___ e bois, e os deu a Abimeleque; e ambos fizeram aliança.", "count": "4", "quiz_index": "26", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Luke 22:61", "update_time": null, "id": "24", "level": "23", "question": "___ negou <PERSON> três vezes.", "count": "4", "quiz_index": "22", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Carpinteiro", "ref": "Matthew 13:55", "update_time": null, "id": "25", "level": "24", "question": "Qual era o trabalho de <PERSON> antes de começar seu ministério?", "count": "4", "quiz_index": "23", "error": "[\"<PERSON>pin<PERSON>iro\",\"Discurso\",\"Padre\",\"Professor<PERSON>\"]"}, {"answer": "Moisés", "ref": "Exodus 20:1-26", "update_time": null, "id": "26", "level": "25", "question": "Quem recebeu os 10 Mandamentos de Deus?", "count": "4", "quiz_index": "29", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Mo<PERSON>és\"]"}, {"answer": "Deus", "ref": "<PERSON> 6:3", "update_time": null, "id": "27", "level": "26", "question": "No princípio era o Verbo, E o Verbo estava com Deus, E o Verbo era ___.", "count": "4", "quiz_index": "28", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"Sagrado\",\"Deus\"]"}, {"answer": "<PERSON><PERSON> injusto<PERSON>", "ref": "1 Corinthians 6:9", "update_time": null, "id": "28", "level": "27", "question": "Não sabeis que os injustos não herdarão o reino de Deus?", "count": "4", "quiz_index": "31", "error": "[\"Adúl<PERSON>s\",\"Fornicadores\",\"idólatras\",\"Os injustos\"]"}, {"answer": "Não acreditou", "ref": "Mark 16:13", "update_time": null, "id": "29", "level": "28", "question": "Como os discípulos de <PERSON> reagiram quando souberam que Jesus havia ressuscitado?", "count": "4", "quiz_index": "30", "error": "[\"<PERSON>ão acreditou\",\"<PERSON>ão se importou\",\"Estávamos em êxtase\",\"Fugiu\"]"}, {"answer": "Domingo", "ref": "Mark 16:9", "update_time": null, "id": "30", "level": "29", "question": "Em que dia da semana Jesus ressuscitou?", "count": "4", "quiz_index": "27", "error": "[\"Sexta-feira\",\"Q<PERSON>ta-feira\",\"Segunda-feira\",\"Domingo\"]"}, {"answer": "3 João", "ref": "3 <PERSON> 1:1-14", "update_time": null, "id": "31", "level": "30", "question": "O livro mais curto da Bíblia é __.", "count": "4", "quiz_index": "33", "error": "[\"3 <PERSON>\",\"<PERSON><PERSON>\",\"File<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Genesis 35:22", "update_time": null, "id": "32", "level": "31", "question": " ___ disse: '<PERSON>u Pai é maior do que eu'.", "count": "4", "quiz_index": "34", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "<PERSON> 1:1-10:3", "update_time": null, "id": "33", "level": "32", "question": "disse-lhes: Quem tem ouvidos para ouvir, ___.", "count": "4", "quiz_index": "35", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Genesis 22:2", "update_time": null, "id": "34", "level": "33", "question": "Quem Deus pediu a Abraão que sacrificasse para provar sua lealdade a ele?", "count": "4", "quiz_index": "36", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "40", "ref": "Matthew 4:2", "update_time": null, "id": "35", "level": "34", "question": "Quantos dias Jesus jejuou no deserto?", "count": "4", "quiz_index": "32", "error": "[\"10\",\"22\",\"40\",\"60\"]"}, {"answer": "<PERSON><PERSON><PERSON> para ver", "ref": "Deuteronomy 29:4", "update_time": null, "id": "36", "level": "35", "question": "Mas o Senhor não vos deu um coração para perceber, e ___, e ouvidos para ouvir, até o dia de hoje.", "count": "4", "quiz_index": "43", "error": "[\"Cérebro para pensar\",\"<PERSON><PERSON><PERSON> para ver\",\"<PERSON><PERSON> para andar\",\"Nariz para cheirar\"]"}, {"answer": "Generosidade", "ref": "Gálatas 5:22", "update_time": null, "id": "37", "level": "36", "question": "O fruto do Espírito é Amor, Alegria, Paz, Paciência, Bondade, ___, Fidelidade, Mansidão e Domínio Próprio.", "count": "4", "quiz_index": "42", "error": "[\"Esperança\",\"Calma\",\"Fé\",\"Generosidade\"]"}, {"answer": "Grandes riquezas", "ref": "Proverbs 22:1", "update_time": null, "id": "38", "level": "37", "question": "Ter um bom nome ou encontrar um favor amoroso é melhor do que ___ riquezas, prata e ouro.", "count": "4", "quiz_index": "38", "error": "[\"Vida infinita\",\"Grandes riquezas\",\"Incontáveis ​​servos\",\"Criança obediente\"]"}, {"answer": "O espírito Santo", "ref": "Proverbs 19:15", "update_time": null, "id": "39", "level": "38", "question": "<PERSON> foi concebido por ___.", "count": "4", "quiz_index": "37", "error": "[\"<PERSON>s<PERSON><PERSON>\",\"O espírito Santo\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Eva", "ref": "Genesis 3:20", "update_time": null, "id": "40", "level": "39", "question": "Quem é a esposa de <PERSON>?", "count": "4", "quiz_index": "39", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Abençoar", "ref": "Luke 6:28", "update_time": null, "id": "41", "level": "40", "question": "___ aqueles que te amaldiçoam, E ore por aqueles que te usam maliciosamente.'", "count": "4", "quiz_index": "48", "error": "[\"<PERSON><PERSON>\",\"Abençoar\",\"Amor\",\"Elogio\"]"}, {"answer": "Confiança|Compreensão", "ref": "Proverbs 3:5", "update_time": null, "id": "42", "level": "41", "question": "___ no Senhor de todo o teu coração, e não te estribes no teu próprio ___.", "count": "4", "quiz_index": "44", "error": "[\"Confiança|Justiça\",\"Acredite|Cognição\",\"Confiança|Compreensão\",\"Amor|Intuição\"]"}, {"answer": "Deus", "ref": "Matthew 3:16", "update_time": null, "id": "43", "level": "42", "question": "Quando <PERSON> sa<PERSON>, a voz de quem ele ouviu?", "count": "4", "quiz_index": "47", "error": "[\"De<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"Moisés\"]"}, {"answer": "<PERSON>", "ref": "Matthew 3:13", "update_time": null, "id": "44", "level": "43", "question": "<PERSON><PERSON> bat<PERSON>?", "count": "4", "quiz_index": "45", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"João Batista\",\"Moisés\",\"Peter\"]"}, {"answer": "Um homem", "ref": "Revelation 22:21", "update_time": null, "id": "45", "level": "44", "question": "A última palavra da Bíblia é ___.", "count": "4", "quiz_index": "46", "error": "[\"Um homem\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Isaías 26:4", "update_time": null, "id": "46", "level": "45", "question": "De acordo com Isaías 26:4, o <PERSON><PERSON> é ___.", "count": "4", "quiz_index": "51", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>i\",\"Fabricante de cavalos de balanço\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Genesis 31:40", "update_time": null, "id": "47", "level": "46", "question": "De dia a seca me consumia, e a geada de noite; e meu sono afastou-se do meu ___.", "count": "4", "quiz_index": "50", "error": "[\"Cérebro\",\"Olhos\",\"Coração\",\"Corpo\"]"}, {"answer": "Coração", "ref": "Psalms 51:10", "update_time": null, "id": "48", "level": "47", "question": "Cria em mim um ___ puro, ó Deus; e renova dentro de mim um espírito reto.", "count": "4", "quiz_index": "49", "error": "[\"Corpo\",\"Cora<PERSON>\",\"Mente\",\"Espírito\"]"}, {"answer": "<PERSON>", "ref": "Acts 4:12", "update_time": null, "id": "49", "level": "48", "question": "De acordo com Atos 4:12, quem pode salvar sua alma?", "count": "4", "quiz_index": "53", "error": "[\"<PERSON>\",\"<PERSON>a mãe\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Dois outros\"]"}, {"answer": "Pão", "ref": "Job 3:3", "update_time": null, "id": "50", "level": "49", "question": "'___ significa Corpo de Jesus na ceia do Senhor.", "count": "4", "quiz_index": "52", "error": "[\"<PERSON><PERSON>\",\"Á<PERSON>\",\"Le<PERSON>\",\"Cerveja\"]"}, {"answer": "Deus", "ref": "<PERSON> 1:1", "update_time": null, "id": "51", "level": "50", "question": "No princípio era o Verbo, E o Verbo estava com ___, E o Verbo era Deus.", "count": "4", "quiz_index": "54", "error": "[\"<PERSON><PERSON>\",\"De<PERSON>\",\"Amor\",\"Sagrado\"]"}, {"answer": "R<PERSON><PERSON>", "ref": "Exodus 8:6", "update_time": null, "id": "52", "level": "51", "question": "De acordo com Êxodo 8:6, o ___ subiu e cobriu a terra do Egito.", "count": "4", "quiz_index": "55", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>qu<PERSON><PERSON>\",\"Peixe\",\"Tartaruga\"]"}, {"answer": "Inocente", "ref": "Exodus 23:7", "update_time": null, "id": "53", "level": "52", "question": "Mantenha-te longe de um assunto falso; e o ___ e o justo não matarás, porque eu não justificarei o ímpio.", "count": "4", "quiz_index": "56", "error": "[\"<PERSON><PERSON>\",\"Corajoso\",\"Inocente\",\"Simpático\"]"}, {"answer": "Pão", "ref": "Genesis 14:18", "update_time": null, "id": "54", "level": "53", "question": "<PERSON>, re<PERSON> <PERSON>, trouxe ___ e vinho: e ele era o sacerdote do Deus Altíssimo.", "count": "4", "quiz_index": "62", "error": "[\"<PERSON><PERSON>z\",\"Le<PERSON>\",\"<PERSON>lor\",\"P<PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Genesis 27:41", "update_time": null, "id": "55", "level": "54", "question": "O que <PERSON>ú queria fazer depois que <PERSON><PERSON><PERSON> 'roubou' a primogenitura?", "count": "4", "quiz_index": "59", "error": "[\"<PERSON><PERSON> se importe\",\"<PERSON><PERSON>\",\"Matar sua mãe\",\"Retome o direito de primogenitura\"]"}, {"answer": "Corpo de jesus", "ref": "Matthew 5:3-10", "update_time": null, "id": "56", "level": "55", "question": "O que o pão representa na Ceia do Senhor?", "count": "4", "quiz_index": "64", "error": "[\"Sangue de Jesus\",\"Corpo de jesus\",\"Comi<PERSON>\",\"Etiqueta\"]"}, {"answer": "Luz", "ref": "Genesis 1:20-23", "update_time": null, "id": "57", "level": "56", "question": "O que Deus criou no primeiro dia da criação?", "count": "4", "quiz_index": "57", "error": "[\"<PERSON><PERSON>\",\"Plantas de solo seco \\u0026\",\"Sol, lua \\u0026 estrelas\",\"Animais terrestres \\u0026 humanos\"]"}, {"answer": "Quarto", "ref": "Exodus 20:8", "update_time": null, "id": "58", "level": "57", "question": "Que mandamento diz: 'Lembre-se do dia de sábado, para santificá-lo.'", "count": "4", "quiz_index": "61", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Quarto\",\"Décimo\"]"}, {"answer": "Mal", "ref": "Psalms 23:4", "update_time": null, "id": "59", "level": "58", "question": "<PERSON><PERSON>, ainda que eu ande pelo vale da sombra da morte, não temerei ___: porque tu estás comigo.", "count": "4", "quiz_index": "60", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Mal\",\"<PERSON><PERSON>\"]"}, {"answer": "Sa<PERSON><PERSON>", "ref": "<PERSON> 6:4", "update_time": null, "id": "60", "level": "59", "question": "O rei Sal<PERSON> pediu ___ de <PERSON>.", "count": "4", "quiz_index": "63", "error": "[\"Fort<PERSON>\",\"Sabed<PERSON>\",\"Família\",\"Amor\"]"}, {"answer": "O Servo Implacável", "ref": "Luke 10:25-37", "update_time": null, "id": "61", "level": "60", "question": "Que parábola diz que devemos perdoar também aqueles que pecam contra nós?", "count": "4", "quiz_index": "67", "error": "[\"<PERSON><PERSON><PERSON>\",\"Rico Louco\",\"O Servo Implacável\",\"Semente de Mostarda\"]"}, {"answer": "Juízes", "ref": "Judges 21:25", "update_time": null, "id": "62", "level": "61", "question": "'Naqueles dias não havia rei em Israel: cada homem fazia o que parecia certo aos seus próprios olhos.' é de ___.", "count": "4", "quiz_index": "69", "error": "[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Sal<PERSON>\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Proverbs 24:17", "update_time": null, "id": "63", "level": "62", "question": "Jesus foi criado principalmente em que lugar quando era criança?", "count": "4", "quiz_index": "72", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Egito\"]"}, {"answer": "Eles ofereceram fogo estranho", "ref": "Numbers 3:4", "update_time": null, "id": "64", "level": "63", "question": "Nadabe e Abiú morreram diante do Senhor, quando ___ diante do Senhor, no deserto...", "count": "4", "quiz_index": "66", "error": "[\"Eles entraram\",\"Eles ofereceram fogo estranho\",\"Tocaram na arca\",\"Não sabemos\"]"}, {"answer": "<PERSON><PERSON>", "ref": "<PERSON> 14:5", "update_time": null, "id": "65", "level": "64", "question": "___ foi preso por mil anos.", "count": "4", "quiz_index": "73", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"Moisés\"]"}, {"answer": "Fé", "ref": "1 <PERSON> 1:5", "update_time": null, "id": "66", "level": "65", "question": "Somos guardados através de ___ para a salvação pelo poder de Deus.", "count": "4", "quiz_index": "74", "error": "[\"Confian<PERSON>\",\"Ter esperança\",\"Amor\",\"Fé\"]"}, {"answer": "Noé", "ref": "Genesis 9:21", "update_time": null, "id": "67", "level": "66", "question": "___ foi a primeira pessoa na Bíblia registrada como ficando bêbada.", "count": "4", "quiz_index": "70", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "No templo", "ref": "Luke 2:46", "update_time": null, "id": "68", "level": "67", "question": "Jesus foi deixado em Jerusalém quando tinha 12 anos. Onde seus pais o encontraram?", "count": "4", "quiz_index": "71", "error": "[\"De<PERSON>ixo de uma árvore\",\"Pelo Rio\",\"Em uma montanha\",\"No templo\"]"}, {"answer": "<PERSON>", "ref": "1 Kings 5:2-7:51", "update_time": null, "id": "69", "level": "68", "question": "___ foi o único a levar uma vida sem pecado.", "count": "4", "quiz_index": "65", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "Ambidestria", "ref": "1 Chronicles 12:2", "update_time": null, "id": "70", "level": "69", "question": "Que acrobacias os apoiadores de Davi possuíam quando Davi estava se escondendo de Saul?", "count": "4", "quiz_index": "68", "error": "[\"Ambidestria\",\"Visão excepcional\",\"audição excepcional\",\"Prever futuro\"]"}, {"answer": "30", "ref": "2 <PERSON> 5:4", "update_time": null, "id": "71", "level": "70", "question": "Quantos anos David tinha quando se tornou rei?", "count": "4", "quiz_index": "77", "error": "[\"20\",\"25\",\"30\",\"40\"]"}, {"answer": "Vermelho", "ref": "Matthew 2:8", "update_time": null, "id": "72", "level": "71", "question": "<PERSON><PERSON><PERSON> a<PERSON> ___ mar.", "count": "4", "quiz_index": "84", "error": "[\"Arab<PERSON>\",\"Preto\",\"Vermelho\",\"Mediterrâneo\"]"}, {"answer": "Bezerro de Ouro", "ref": "Exodus 32:15-35", "update_time": null, "id": "73", "level": "72", "question": "Quando Moisés voltou do Monte Sinai com 10 mandamentos, o ___ era um falso ídolo dos israelitas", "count": "4", "quiz_index": "75", "error": "[\"Bezerro de Ouro\",\"Cordeiro de Ouro\",\"Cavalo de Ouro\",\"Cão de Ouro\"]"}, {"answer": "Fam<PERSON><PERSON>", "ref": "1 Kings 14:2", "update_time": null, "id": "74", "level": "73", "question": "___ foi salvo do dilúvio.", "count": "4", "quiz_index": "80", "error": "[\"Família de Abraão\",\"Família de isaque\",\"Família de Nabal\",\"Família de noé\"]"}, {"answer": "Páscoa", "ref": "Mark 14:16", "update_time": null, "id": "75", "level": "74", "question": "Que festa os judeus celebravam quando Jesus foi crucificado?", "count": "4", "quiz_index": "79", "error": "[\"Expiação\",\"Israel\",\"Páscoa\",\"Tabernáculos\"]"}, {"answer": "<PERSON>aiu no sono", "ref": "Luke 22:45", "update_time": null, "id": "76", "level": "75", "question": "Enquanto Jesus sofria a Expiação, o que Pedro, Tiago e João fizeram?", "count": "4", "quiz_index": "82", "error": "[\"<PERSON><PERSON><PERSON> no sono\",\"Procurou traidor\",\"Corri para outros países\",\"Tentei parar Jesus\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Acts 12:23", "update_time": null, "id": "77", "level": "76", "question": "___ foi 'comido de vermes' e acabou morrendo.", "count": "4", "quiz_index": "83", "error": "[\"<PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Moisés", "ref": "Matthew 1:17", "update_time": null, "id": "78", "level": "77", "question": "Quem abriu o Mar Vermelho?", "count": "4", "quiz_index": "76", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "A sinagoga", "ref": "Mark 3:1-5", "update_time": null, "id": "79", "level": "78", "question": "Onde Jesus encontrou e curou o homem com a mão mirrada?", "count": "4", "quiz_index": "81", "error": "[\"O mercado\",\"A marcenaria\",\"A piscina de Betesda\",\"A sinagoga\"]"}, {"answer": "<PERSON><PERSON> não lavou as m<PERSON><PERSON>", "ref": "<PERSON> 11:38", "update_time": null, "id": "80", "level": "79", "question": "Por que os fariseus ficaram surpresos quando Jesus jantou em sua casa?", "count": "4", "quiz_index": "78", "error": "[\"Porque Jesus comeu carne\",\"Porque Jesus não comeu\",\"Porque Jesus não lavou as mãos\",\"Porque Jesus não se sentou\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Luke 3:23", "update_time": null, "id": "81", "level": "80", "question": "A vara de <PERSON>-se um ____.", "count": "4", "quiz_index": "87", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Par de asas\"]"}, {"answer": "<PERSON>v<PERSON><PERSON><PERSON> 4:24", "ref": "Proverbs 4:24", "update_time": null, "id": "82", "level": "81", "question": "___ men<PERSON><PERSON><PERSON> 'Desvia de ti a tortuosidade da boca e alonga de ti a perversidade dos lábios.'", "count": "4", "quiz_index": "94", "error": "[\"<PERSON> 3:2\",\"<PERSON> 16:9\",\"<PERSON> 9:36\",\"<PERSON><PERSON><PERSON><PERSON><PERSON> 4:24\"]"}, {"answer": "Moisés", "ref": "Genesis 31:19", "update_time": null, "id": "83", "level": "82", "question": "Quem foi adotado pela filha do faraó?", "count": "4", "quiz_index": "91", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Moisés\"]"}, {"answer": "Sa<PERSON><PERSON>", "ref": "Proverbs 16:16", "update_time": null, "id": "84", "level": "83", "question": "Quão melhor é obter ___ do que ouro e obter entendimento em vez de ser escolhido do que prata.", "count": "4", "quiz_index": "85", "error": "[\"<PERSON><PERSON><PERSON>\",\"Amor\",\"Ter esperança\",\"Ajuda\"]"}, {"answer": "Deixar o país de origem", "ref": "Genesis 12:1", "update_time": null, "id": "85", "level": "84", "question": "O que Senhor disse a Abrão para fazer?", "count": "4", "quiz_index": "86", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"Deixar o país de origem\",\"Missão de pregação\",\"Me casar\"]"}, {"answer": "Salomão", "ref": "1 Kings 2:12", "update_time": null, "id": "86", "level": "85", "question": "Quem se tornou o rei com a morte de Davi?", "count": "4", "quiz_index": "89", "error": "[\"Ad<PERSON><PERSON>\",\"Nat<PERSON>\",\"Salomão\",\"Absalão\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "<PERSON> 7:5", "update_time": null, "id": "87", "level": "86", "question": "Que insetos eram comestíveis de acordo com a Lei?", "count": "4", "quiz_index": "93", "error": "[\"Escorpiões\",\"Aranhas\",\"Abelhas\",\"Gafanhotos\"]"}, {"answer": "Estou nu", "ref": "Genesis 3:10", "update_time": null, "id": "88", "level": "87", "question": "Quando Deus perguntou a Adão 'Onde você está?', como Ele respondeu?", "count": "4", "quiz_index": "88", "error": "[\"<PERSON><PERSON><PERSON> ocupado\",\"<PERSON>u estou perdido\",\"<PERSON>stou nu\",\"Estamos descansando\"]"}, {"answer": "600", "ref": "Genesis 11:10-11", "update_time": null, "id": "89", "level": "88", "question": "Quantos anos Shem viveu?", "count": "4", "quiz_index": "90", "error": "[\"300\",\"500\",\"600\",\"800\"]"}, {"answer": "40", "ref": "Obadiah 1:1-21", "update_time": null, "id": "90", "level": "89", "question": "Jesus foi tentado no deserto por ___ dias.", "count": "4", "quiz_index": "92", "error": "[10,20,30,40]"}, {"answer": "A mente e o coração", "ref": "Hebrews 10:16", "update_time": null, "id": "91", "level": "90", "question": "Quem Deus designou Josué para suceder Israel como líder?", "count": "4", "quiz_index": "96", "error": "[\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"A mente e o coração\",\"Têmpora\",\"Seu corpo\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Joshua 1:1-2", "update_time": null, "id": "92", "level": "91", "question": "<PERSON> entrou em Jerusalém em um _____ para mostrar publicamente que ele era o Filho escolhido <PERSON>.", "count": "4", "quiz_index": "103", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>de\"]"}, {"answer": "<PERSON>", "ref": "<PERSON> 17:3", "update_time": null, "id": "93", "level": "92", "question": "E esta é a vida eterna, que te conheçam, o único Deus verdadeiro, e ___, a quem enviaste.", "count": "4", "quiz_index": "102", "error": "[\"<PERSON><PERSON>\",\"Jesus Cristo\",\"Os profetas\",\"Espírito\"]"}, {"answer": "Eu sou ele", "ref": "<PERSON> 8:24", "update_time": null, "id": "94", "level": "93", "question": "Morrereis em vossos pecados, porque se não crerdes que ___, morrereis em vossos pecados.", "count": "4", "quiz_index": "104", "error": "[\"Deus levanta o justo\",\"Eu sou ele\",\"Eu sou leve\",\"Sou filho de Deus\"]"}, {"answer": "Obra-prima", "ref": "Ephesians 2:10", "update_time": null, "id": "95", "level": "94", "question": "O que Efésios 2:10 diz que você é?", "count": "4", "quiz_index": "95", "error": "[\"neto\",\"<PERSON><PERSON>\",\"Obra-prima\",\"Presente\"]"}, {"answer": "<PERSON>", "ref": "1 Kings 18:46", "update_time": null, "id": "96", "level": "95", "question": "A primeira esposa de Abraão foi ___.", "count": "4", "quiz_index": "97", "error": "[\"<PERSON>gar\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>hoda\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Isaiah 7:14", "update_time": null, "id": "97", "level": "96", "question": "Quem profetizou 'Uma virgem conceberá e dará à luz um filho'.", "count": "4", "quiz_index": "98", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>", "ref": "Matthew 3:2", "update_time": null, "id": "98", "level": "97", "question": "___ veio pregando no deserto da Judéia, e dizendo: Arrependei-vos, porque o reino dos céus está próximo.", "count": "4", "quiz_index": "101", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>, <PERSON> Ousado\",\"João Batista\",\"Esaias, o profeta\"]"}, {"answer": "Sétimo", "ref": "Genesis 7:24", "update_time": null, "id": "99", "level": "98", "question": "Deus descansou no ___ dia da criação.", "count": "4", "quiz_index": "99", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Primeiro\"]"}, {"answer": "0.1", "ref": "Números 18:26", "update_time": null, "id": "100", "level": "99", "question": "Os levitas foram instruídos a dar o dízimo a Deus, qual a porcentagem do dízimo dos israelitas?", "count": "4", "quiz_index": "100", "error": "[\"0.1\",\"0.2\",\"0.3\",\"0.4\"]"}, {"answer": "<PERSON>", "ref": "Genesis 9:29", "update_time": null, "id": "101", "level": "100", "question": "No livro de Hebreus, ___ é \"o autor e consumador da nossa fé\" @Jesus", "count": "4", "quiz_index": "123", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "Noé", "ref": "Genesis 5:32", "update_time": null, "id": "102", "level": "101", "question": "Quemera o pai de <PERSON>?", "count": "4", "quiz_index": "114", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON>e vezes", "ref": "2 Kings 4:35", "update_time": null, "id": "103", "level": "102", "question": "Quantas vezes o jovem Shunamm espirrou quando Eliseu o reviveu?", "count": "4", "quiz_index": "109", "error": "[\"Quatro vezes\",\"Sete vezes\",\"Oito vezes\",\"Dez vezes\"]"}, {"answer": "14", "ref": "Genesis 29:18-30", "update_time": null, "id": "104", "level": "103", "question": "Quantos anos <PERSON> t<PERSON> para a fi<PERSON>ha <PERSON>, Rachel?", "count": "4", "quiz_index": "108", "error": "[\"14\",\"17\",\"12\",\"15\"]"}, {"answer": "6", "ref": "Exodus 7:7", "update_time": null, "id": "105", "level": "104", "question": "Deus levou ___ dias para criar o mundo.", "count": "4", "quiz_index": "110", "error": "[40,10,6,7]"}, {"answer": "Conhecimento", "ref": "Proverbs 12:23", "update_time": null, "id": "106", "level": "105", "question": "O homem prudente esconde ___; mas o coração dos tolos proclama a loucura.", "count": "4", "quiz_index": "121", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Conhecimento\",\"Ignorância\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>", "ref": "2 Corinthians 12:7-8", "update_time": null, "id": "107", "level": "106", "question": "Quantas vezes Paulo pediu a Deus para remover seu'espinho na carne'？", "count": "4", "quiz_index": "120", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"]"}, {"answer": "Casar com sua filha", "ref": "1 Kings 3:1", "update_time": null, "id": "108", "level": "107", "question": "Salomão fez um acordo com o faraó rei do Egito por ___.", "count": "4", "quiz_index": "113", "error": "[\"Dando-lhe terra\",\"Casar com sua filha\",\"Protegendo o Egito\",\"Trocando mercadorias com ele\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Isaiah 7:14", "update_time": null, "id": "109", "level": "108", "question": "Eis que uma virgem conceberá e dará à luz um filho, e chamará o seu nome Emanuel.' Em que livro é este?", "count": "4", "quiz_index": "107", "error": "[\"<PERSON><PERSON>\",\"<PERSON>rem<PERSON>\",\"Trabalho\",\"Zacarias\"]"}, {"answer": "A glória se foi de Israel", "ref": "1 <PERSON> 4:21", "update_time": null, "id": "110", "level": "109", "question": "O nome Ichabod significa ___.", "count": "4", "quiz_index": "105", "error": "[\"Persona não grata\",\"<PERSON>lho do meu ventre\",\"Glória de Deus\",\"A glória se foi de Israel\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Genesis 12:5", "update_time": null, "id": "111", "level": "110", "question": "<PERSON> criou seu sobrinho ___.", "count": "4", "quiz_index": "116", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "A sua cabeça", "ref": "Psalms 23:5", "update_time": null, "id": "112", "level": "111", "question": "Onde o senhor ungiu com óleo", "count": "4", "quiz_index": "126", "error": "[\"A comida dele\",\"As mãos dele\",\"A sua cabeça\",\"Seu pé\"]"}, {"answer": "Boca|Coração", "ref": "Isaiah 29:13", "update_time": null, "id": "113", "level": "112", "question": "Este povo se aproxima de mim com seus ___, e ..., mas removeram seus ___ para longe de mim,", "count": "4", "quiz_index": "122", "error": "[\"Lábios|Coração\",\"Mãos|Pés\",\"Coração|Mentes\",\"Boca|Coração\"]"}, {"answer": "1000", "ref": "Judges 15:16", "update_time": null, "id": "114", "level": "113", "question": "<PERSON><PERSON><PERSON> matou ___ filisteus com uma queixada de burro?", "count": "4", "quiz_index": "124", "error": "[\"1000\",\"200\",\"500\",\"800\"]"}, {"answer": "<PERSON><PERSON>", "ref": "<PERSON> 7:15", "update_time": null, "id": "115", "level": "114", "question": "De qual livro vem 'Lobo em roupa de ovelha'?", "count": "4", "quiz_index": "125", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "A sexta hora", "ref": "<PERSON> 4:6", "update_time": null, "id": "116", "level": "115", "question": "<PERSON>, cansado depois de sua viagem, estava descansando junto à fonte. Era cerca de ___.", "count": "4", "quiz_index": "106", "error": "[\"A sétima hora\",\"A sexta hora\",\"O sexto minuto\",\"O sétimo segundo\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "<PERSON> 30:17", "update_time": null, "id": "117", "level": "116", "question": "Porque te restituirei ___, e te curarei das tuas feridas, diz o Senhor.", "count": "4", "quiz_index": "111", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Fiel\"]"}, {"answer": "Um centurião", "ref": "Matthew 8:8", "update_time": null, "id": "118", "level": "117", "question": "___ disse: '<PERSON><PERSON>, eu não sou digno: fale apenas a palavra, e meu servo será curado'.", "count": "4", "quiz_index": "112", "error": "[\"Um centurião\",\"Viúva de Naim\",\"Jairo\",\"Nicodemos\"]"}, {"answer": "<PERSON>", "ref": "Revelation 22:16", "update_time": null, "id": "119", "level": "118", "question": "<PERSON>m se chamava 'The Bright and Morning Star'.", "count": "4", "quiz_index": "115", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "E<PERSON>ias<PERSON>", "ref": "Ecclesiastes 3:1", "update_time": null, "id": "120", "level": "119", "question": " ___ men<PERSON><PERSON><PERSON>'Para cada coisa há uma estação, e um tempo para cada propósito debaixo do céu'.", "count": "4", "quiz_index": "119", "error": "[\"Eclesiastes\",\"Isaías\",\"Salmos\",\"Ezequiel\"]"}, {"answer": "Belém", "ref": "1 <PERSON> 16:1-13", "update_time": null, "id": "121", "level": "120", "question": "onde Davi foi ungido rei de Israel?", "count": "4", "quiz_index": "127", "error": "[\"Samária\",\"<PERSON><PERSON>\",\"Belém\",\"Na<PERSON><PERSON>\"]"}, {"answer": "Hip<PERSON><PERSON><PERSON>", "ref": "Luke 12:1", "update_time": null, "id": "122", "level": "121", "question": "O fermento dos fariseus é ___.", "count": "4", "quiz_index": "149", "error": "[\"Amar<PERSON>ra\",\"Hipocrisia\",\"Fé\",\"Vaidade\"]"}, {"answer": "A árvore da Vida", "ref": "Revelation 22:2", "update_time": null, "id": "123", "level": "122", "question": "Um rio corre no meio da rua. As árvores de ambos os lados se chamam ___.", "count": "4", "quiz_index": "147", "error": "[\"A árvore do amor\",\"A árvore da Vida\",\"A árvore da esperança\",\"A árvore da pureza\"]"}, {"answer": "<PERSON><PERSON>", "ref": "1 Chronicles 22:7", "update_time": null, "id": "124", "level": "123", "question": "Quem primeiro desejou construir um templo para o Senhor.", "count": "4", "quiz_index": "140", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Abr<PERSON>\",\"<PERSON>\"]"}, {"answer": "Ramo | Murchado", "ref": "<PERSON> 15:6", "update_time": null, "id": "125", "level": "124", "question": "Se um homem não permanece em mim, ele é lançado como um ___, e é ___;", "count": "4", "quiz_index": "157", "error": "[\"Ramo | Murchado\",\"Ramo|Queimado\",\"Folha | Murchada\",\"Galho | Queimado\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Judges 3:21-22", "update_time": null, "id": "126", "level": "125", "question": "<PERSON> foi assassinado por ___.", "count": "4", "quiz_index": "135", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Midia", "ref": "Exodus 2:15", "update_time": null, "id": "127", "level": "126", "question": "Para onde Moisés fugiu depois de matar o egípcio?", "count": "4", "quiz_index": "150", "error": "[\"Adm<PERSON>\",\"Mid<PERSON>\",\"Sodoma\",\"Zoar\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Exodus 7:1-11:10", "update_time": null, "id": "128", "level": "127", "question": "Dez Pragas do Egito não incluem___.", "count": "4", "quiz_index": "128", "error": "[\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"piol<PERSON>\",\"Serpent<PERSON>\"]"}, {"answer": "Paulo", "ref": "Acts 22:25", "update_time": null, "id": "129", "level": "128", "question": "Quem era um cidadão romano entre doze apóstolos.", "count": "4", "quiz_index": "145", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Fogo e enxofre", "ref": "Genesis 19:24", "update_time": null, "id": "130", "level": "129", "question": "O Senhor destruiu Sodoma e Gomorra com ___", "count": "4", "quiz_index": "155", "error": "[\"Uma inundação\",\"Um exército\",\"Um terremoto\",\"Fogo e enxofre\"]"}, {"answer": "<PERSON>", "ref": "Acts 22:21", "update_time": null, "id": "131", "level": "130", "question": "Quem orientou Paulo a pregar aos gentios?", "count": "4", "quiz_index": "142", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Proverbs 20:28", "update_time": null, "id": "132", "level": "131", "question": "___ perde<PERSON> suas riquezas, Saúde e família, <PERSON><PERSON> per<PERSON><PERSON><PERSON> fiel a Deus em suas aflições", "count": "4", "quiz_index": "144", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"Mesa<PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "Gênesis 22", "ref": "Genesis 22:1-24", "update_time": null, "id": "133", "level": "132", "question": "Em ___, <PERSON><PERSON><PERSON> tenta sacrificar Isaque.", "count": "4", "quiz_index": "131", "error": "[\"Êxodo 5\",\"Números 12\",\"Moisés 10\",\"Gênesis 22\"]"}, {"answer": "Grão", "ref": "Genesis 41:57", "update_time": null, "id": "134", "level": "133", "question": "O que as pessoas vieram a <PERSON> e ao Egito para comprar durante a fome egípcia?", "count": "4", "quiz_index": "151", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "O temor do Senhor", "ref": "Psalms 111:10", "update_time": null, "id": "135", "level": "134", "question": "Salmo 111:10 diz, ___ é o fundamento da verdadeira sabedoria.", "count": "4", "quiz_index": "153", "error": "[\"O amor do <PERSON>hor\",\"Conseguir um diploma\",\"Ser humilde\",\"O temor do Senhor\"]"}, {"answer": "<PERSON><PERSON><PERSON><PERSON>", "ref": "Psalms 133:1", "update_time": null, "id": "136", "level": "135", "question": "Eis que quão bom e quão ___ é que os irmãos vivam em união!", "count": "4", "quiz_index": "137", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Praze<PERSON>o\",\"Confiável\"]"}, {"answer": "Lamentações", "ref": "Lamentations 1:1", "update_time": null, "id": "137", "level": "136", "question": "'Como se acha solitária aquela cidade dantes tão populosa!', Qual livro é o início desta frase?", "count": "4", "quiz_index": "146", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Lamentações\",\"<PERSON>\"]"}, {"answer": "Deus", "ref": "Genesis 32:28", "update_time": null, "id": "138", "level": "137", "question": "Quem mudou o nome de <PERSON>?", "count": "4", "quiz_index": "138", "error": "[\"Deus\",\"<PERSON><PERSON><PERSON>\",\"Ju<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "<PERSON>ris<PERSON><PERSON>", "ref": "Matthew 27:15", "update_time": null, "id": "139", "level": "138", "question": "O governador normalmente liberado era um ___ da escolha do povo.", "count": "4", "quiz_index": "152", "error": "[\"Homem sábio\",\"Prisioneiro\",\"Cidadão\",\"Professora\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Genesis 14:1", "update_time": null, "id": "140", "level": "139", "question": "___ identifcado o reino de Chedorlaomer.", "count": "4", "quiz_index": "133", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Gomorra\",\"<PERSON><PERSON><PERSON><PERSON>\"]"}, {"answer": "30", "ref": "<PERSON> of Solomon 8:11", "update_time": null, "id": "141", "level": "140", "question": "Quantos anos Jesus tinha quando começou seu ministério?", "count": "4", "quiz_index": "129", "error": "[12,20,30,40]"}, {"answer": "Limpou suas feridas", "ref": "Luke 10:34", "update_time": null, "id": "142", "level": "141", "question": "O que o bom samaritano fez por um homem que foi espancado até a morte por ladrões?", "count": "4", "quiz_index": "130", "error": "[\"<PERSON><PERSON><PERSON> suas feridas\",\"<PERSON><PERSON> por ele\",\"Leve-o para casa\",\"Orei por ele\"]"}, {"answer": "Um quinto", "ref": "Genesis 47:24", "update_time": null, "id": "143", "level": "142", "question": "Quando <PERSON> der sementes aos egípcios, quanto de suas colheitas irá para o Faraó em troca?", "count": "4", "quiz_index": "156", "error": "[\"<PERSON><PERSON>\",\"<PERSON> quinto\",\"T<PERSON>ês quintos\",\"Quatro quintos\"]"}, {"answer": "Enviar uma inundação", "ref": "Genesis 9:18", "update_time": null, "id": "144", "level": "143", "question": "Deus disse que iria ___ porque o povo não quis ouvir <PERSON>.", "count": "4", "quiz_index": "136", "error": "[\"<PERSON>azer fogo\",\"Faça um terremoto\",\"Enviar uma inundação\",\"Faça uma fome\"]"}, {"answer": "zomba disso", "ref": "Proverbs 14:9", "update_time": null, "id": "145", "level": "144", "question": "O que o tolo faz do pecado, de acordo com Provérbios 14?", "count": "4", "quiz_index": "141", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON> se importe\",\"<PERSON>de<PERSON>\",\"zomba disso\"]"}, {"answer": "<PERSON>", "ref": "<PERSON> 1:2", "update_time": null, "id": "146", "level": "145", "question": "Quem foi eleito líder de Israel após a morte de Moisés?", "count": "4", "quiz_index": "154", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "Uma torre forte", "ref": "Proverbs 18:10", "update_time": null, "id": "147", "level": "146", "question": "O nome do Senhor é ___: o justo corre para ela, e está seguro.", "count": "4", "quiz_index": "132", "error": "[\"Uma caverna profunda\",\"Uma cidade inquebrável\",\"Uma torre forte\",\"Um exército disciplinado\"]"}, {"answer": "<PERSON><PERSON>", "ref": "1 <PERSON> 17:12", "update_time": null, "id": "148", "level": "147", "question": "Quem era o pai de <PERSON>?", "count": "4", "quiz_index": "134", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "1 <PERSON> 3:20", "update_time": null, "id": "149", "level": "148", "question": "O batismo também agora nos salva, assim como ___ almas foram salvas pela água durante os dias de Noé.", "count": "4", "quiz_index": "139", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Luke 2:24", "update_time": null, "id": "150", "level": "149", "question": "___ é comumente chamado de 'Bem-aventuranças'.", "count": "4", "quiz_index": "143", "error": "[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Leviticus 4:2-3", "update_time": null, "id": "151", "level": "150", "question": "No tempo de Moisés, o que uma pessoa tinha que sacrificar para receber o perdão se pecasse por ignorância?", "count": "4", "quiz_index": "181", "error": "[\"<PERSON>i\",\"<PERSON>rda<PERSON>\",\"Cabra\",\"Pombo\"]"}, {"answer": "E<PERSON>ias<PERSON>", "ref": "Ecclesiastes 1:1-12:14", "update_time": null, "id": "152", "level": "151", "question": "Qual livro da Bíblia foi escrito por Salomão?", "count": "4", "quiz_index": "227", "error": "[\"<PERSON><PERSON><PERSON><PERSON>\",\"Êxodo\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "1 <PERSON> 21:13", "update_time": null, "id": "153", "level": "152", "question": "Quem fingiu ser louco para evitar a captura e a morte nas mãos de um rei inimigo?", "count": "4", "quiz_index": "190", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Matthew 6:17", "update_time": null, "id": "154", "level": "153", "question": "O que devemos fazer com nosso rosto quando jejuamos?", "count": "4", "quiz_index": "233", "error": "[\"<PERSON><PERSON>ra-o\",\"Não toque\",\"Coloque cinzas nele\",\"Lavar\"]"}, {"answer": "Um fazendeiro", "ref": "<PERSON> 5:7", "update_time": null, "id": "155", "level": "154", "question": "Tiago usa ___ como exemplo para encorajar as pessoas a serem pacientes para o retorno do Senhor.", "count": "4", "quiz_index": "216", "error": "[\"Um fazendeiro\",\"<PERSON>a mãe\",\"Um profeta\",\"Um soldado\"]"}, {"answer": "Cerca de 1 ano", "ref": "Genesis 7:11-8:14", "update_time": null, "id": "156", "level": "155", "question": "Quanto tempo Noé passou na arca?", "count": "4", "quiz_index": "221", "error": "[\"1 ano e meio\",\"Cerca de 1 ano\",\"Cerca de 6 meses\",\"Cerca de 9 meses\"]"}, {"answer": "Macedônia", "ref": "Acts 16:9", "update_time": null, "id": "157", "level": "156", "question": "E uma visão apareceu a Paulo durante a noite; ali estava um homem de ___, e orou por ele.", "count": "4", "quiz_index": "206", "error": "[\"<PERSON><PERSON>\",\"Bitín<PERSON>\",\"Macedônia\",\"Frígia\"]"}, {"answer": "<PERSON>a pousada", "ref": "Luke 10:34", "update_time": null, "id": "158", "level": "157", "question": "O samaritano levou o homem que foi ferido para ___.", "count": "4", "quiz_index": "212", "error": "[\"Um médico\",\"<PERSON>a pousada\",\"Jerusalém\",\"Os sacerdotes\"]"}, {"answer": "Ferro|Amigo", "ref": "Proverbs 27:17", "update_time": null, "id": "159", "level": "158", "question": "O ferro afia ___; assim um homem afia o semblante de seu ___.", "count": "4", "quiz_index": "188", "error": "[\"Flint|Pai\",\"Ferro|Amigo\",\"Rock|Esposa\",\"Pedra|Cara\"]"}, {"answer": "Foi do Senhor", "ref": "1 <PERSON> 1:20", "update_time": null, "id": "160", "level": "159", "question": "A razão pela qual Hannah chamou seu filho de '<PERSON>' foi porque ele ___.", "count": "4", "quiz_index": "229", "error": "[\"Era uma criança piedosa\",\"Foi um nazireu\",\"Foi do Senhor\",\"Foi tranquila\"]"}, {"answer": "16", "ref": "2 Kings 11:21", "update_time": null, "id": "161", "level": "160", "question": "<PERSON><PERSON><PERSON> ___ anos quando come<PERSON> a reinar.", "count": "4", "quiz_index": "199", "error": "[\"16\",\"18\",\"20\",\"22\"]"}, {"answer": "Indecisão", "ref": "Ephesians 5:1-6", "update_time": null, "id": "162", "level": "161", "question": "O que não causa a ira de Deus sobre os filhos da desobediência?", "count": "4", "quiz_index": "215", "error": "[\"Cobiça\",\"Impureza\",\"Indecisão\",\"Luxúria e maus desejos\"]"}, {"answer": "Madeira de cedro", "ref": "1 Kings 5:6", "update_time": null, "id": "163", "level": "162", "question": "___ produtos do Líbano foram usados ​​na construção do templo de Salomão.", "count": "4", "quiz_index": "217", "error": "[\"<PERSON><PERSON><PERSON>\",\"Madeira de cedro\",\"Ouro\",\"Peles de Carneiro\"]"}, {"answer": "Corinto", "ref": "Acts 18:12-15", "update_time": null, "id": "164", "level": "163", "question": "Paulo ganhou um julgamento favorável por Gallio, um deputado romano, contra os judeus em ___.", "count": "4", "quiz_index": "187", "error": "[\"Antióquia\",\"Corinto\",\"Derbe\",\"Lista\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Mark 10:46-52", "update_time": null, "id": "165", "level": "164", "question": "Qual era o nome do cego que pediu a <PERSON> para restaurar sua visão em Jericó?", "count": "4", "quiz_index": "196", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\"]"}, {"answer": "600", "ref": "Genesis 7:6", "update_time": null, "id": "166", "level": "165", "question": "<PERSON><PERSON> ___ anos de idade quando as águas do dilúvio caíram sobre a face da terra.", "count": "4", "quiz_index": "223", "error": "[\"300\",\"500\",\"600\",\"750\"]"}, {"answer": "<PERSON>", "ref": "John 20:24-25", "update_time": null, "id": "167", "level": "166", "question": "Exceto eu vou... Enfiar minha mão no lado dele, eu não vou acreditar. Quem disse isso?", "count": "4", "quiz_index": "201", "error": "[\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Onde está teu irmão?", "ref": "Genesis 4:9", "update_time": null, "id": "168", "level": "167", "question": "Depois que <PERSON><PERSON> mat<PERSON>, qual é a primeira coisa que o Senhor lhe pergunta?", "count": "4", "quiz_index": "231", "error": "[\"O que você fez?\",\"Onde está teu irmão?\",\"Que<PERSON> mat<PERSON>?\",\"Por que você fez isso?\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Genesis 22:11", "update_time": null, "id": "169", "level": "168", "question": "Qual deles impediu Abraão de matar seu filho?", "count": "4", "quiz_index": "208", "error": "[\"An<PERSON> Senhor\",\"<PERSON>e par<PERSON> a si mesmo\",\"Ninguém\",\"Satanás\"]"}, {"answer": "Tíquico", "ref": "Colossians 4:7", "update_time": null, "id": "170", "level": "169", "question": "<PERSON> escreveu: 'Todo o meu estado ___ declarar a você, que é um irmão amado, e um ministro fiel.'", "count": "4", "quiz_index": "235", "error": "[\"Acaico\",\"Alexandre\",\"Filemon\",\"Tíquico\"]"}, {"answer": "Para todo sempre", "ref": "Isaiah 32:17", "update_time": null, "id": "171", "level": "170", "question": "De acordo com Isaías 32:17, o que você precisa fazer para trazer paz e confiança?", "count": "4", "quiz_index": "197", "error": "[\"Por cerca de 5 minutos\",\"Para todo sempre\",\"Quando você espera\",\"Você é um bom garoto\"]"}, {"answer": "Tudo o que precede", "ref": "Isaiah 9:6", "update_time": null, "id": "172", "level": "171", "question": "Isaías 9:6 prediz que <PERSON> será chamado pelo seguinte nome?", "count": "4", "quiz_index": "207", "error": "[\"<PERSON>do o que precede\",\"Conselheiro\",\"Poderoso Deus\",\"<PERSON>r<PERSON><PERSON><PERSON> da Paz\"]"}, {"answer": "Figos bons e maus", "ref": "Jeremiah 24:2", "update_time": null, "id": "173", "level": "172", "question": "O Senhor mostrou a <PERSON><PERSON><PERSON> duas cestas cheias de ___.", "count": "4", "quiz_index": "228", "error": "[\"<PERSON>go\",\"<PERSON><PERSON>\",\"Figos bons e maus\",\"doces limões\"]"}, {"answer": "Belsazar", "ref": "<PERSON> 5:9", "update_time": null, "id": "174", "level": "173", "question": "___ estava muito preocupado com a escrita na parede.", "count": "4", "quiz_index": "234", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Hero<PERSON>\",\"Salomão\"]"}, {"answer": "Em um Peixe", "ref": "Matthew 17:27", "update_time": null, "id": "175", "level": "174", "question": "Onde Jesus disse a Pedro para encontrar as moedas para pagar o imposto do templo?", "count": "4", "quiz_index": "213", "error": "[\"<PERSON> Templo\",\"Do Sacerdote\",\"Em sua túnica\",\"Em um Peixe\"]"}, {"answer": "marido e mulher", "ref": "Acts 5:1", "update_time": null, "id": "176", "level": "175", "question": "A relação entre Ananias e Safira era ___.", "count": "4", "quiz_index": "222", "error": "[\"<PERSON>rm<PERSON> e irmã\",\"marido e mulher\",\"Vizinhos\",\"Estranhos\"]"}, {"answer": "<PERSON><PERSON>", "ref": "2 <PERSON> 3:27", "update_time": null, "id": "177", "level": "176", "question": "<PERSON><PERSON> ___ no portão de Hebron.", "count": "4", "quiz_index": "205", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Lázar<PERSON>\"]"}, {"answer": "Filipenses", "ref": "Acts 16:21", "update_time": null, "id": "178", "level": "177", "question": "Onde alguns homens acusaram Paulo e Silas, dizendo que 'ensinam costumes que não nos são lícitos'?", "count": "4", "quiz_index": "195", "error": "[\"Atenas\",\"Derbe\",\"Lista\",\"Filipenses\"]"}, {"answer": "Abisague", "ref": "1 Kings 1:2-3", "update_time": null, "id": "179", "level": "178", "question": "Que jovem virgem manteve o idoso rei Davi aquecido?", "count": "4", "quiz_index": "202", "error": "[\"Abisague\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Diblaim\",\"Mical\"]"}, {"answer": "Canção de Salomão", "ref": "Song of Solomon 2:16", "update_time": null, "id": "180", "level": "179", "question": "Meu amado é meu e eu sou dele. De onde ele foi tirado?", "count": "4", "quiz_index": "220", "error": "[\"Eclesiastes\",\"Provérbios\",\"Salmos\",\"Canção de Salomão\"]"}, {"answer": "70", "ref": "Numbers 11:16", "update_time": null, "id": "181", "level": "180", "question": "___ an<PERSON><PERSON><PERSON> foram reunidos por Moisés por ordem de Deus no deserto.", "count": "4", "quiz_index": "224", "error": "[\"105\",\"55\",\"70\",\"92\"]"}, {"answer": "Salmos", "ref": "Psalms 1:1-150:6", "update_time": null, "id": "182", "level": "181", "question": "Qual livro é o mais longo do Antigo Testamento?", "count": "4", "quiz_index": "191", "error": "[\"G<PERSON><PERSON>\",\"<PERSON>rabal<PERSON>\",\"Mat<PERSON>\",\"Sal<PERSON>\"]"}, {"answer": "3", "ref": "Job 2:11", "update_time": null, "id": "183", "level": "182", "question": "Quantos amigos de Jó foram consolá-lo durante seu sofrimento?", "count": "4", "quiz_index": "214", "error": "[\"3\",\"4\",\"5\",\"6\"]"}, {"answer": "Lua e estrelas", "ref": "Genesis 1:14-19", "update_time": null, "id": "184", "level": "183", "question": "No 4º dia da criação, Deus fez ___.", "count": "4", "quiz_index": "193", "error": "[\"An<PERSON><PERSON>\",\"Lua e estrelas\",\"Plantas\",\"Céu\"]"}, {"answer": "<PERSON><PERSON>", "ref": "Acts 16:27", "update_time": null, "id": "185", "level": "184", "question": "Quando o carcer<PERSON> de Phillipi viu as portas da prisão abertas, o que ele tentou fazer?", "count": "4", "quiz_index": "203", "error": "[\"Subornar os funcionários\",\"Cometer suicídio\",\"Relatar\",\"Fugir\"]"}, {"answer": "<PERSON>", "ref": "Matthew 26:36", "update_time": null, "id": "186", "level": "185", "question": "<PERSON> levou ___ com ele para o Get<PERSON>êmani.", "count": "4", "quiz_index": "230", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]"}, {"answer": "Samária", "ref": "Hosea 13:16", "update_time": null, "id": "187", "level": "186", "question": "No décimo terceiro capítulo de Oséias, que terra o Senhor diz que se tornará desolada?", "count": "4", "quiz_index": "226", "error": "[\"Egito\",\"Israel\",\"Jud<PERSON>\",\"Samária\"]"}, {"answer": "<PERSON><PERSON><PERSON> virtuosa", "ref": "Proverbs 31:1-31", "update_time": null, "id": "188", "level": "187", "question": "Provérbios capítulo 31 é principalmente dedicado ao ___.", "count": "4", "quiz_index": "200", "error": "[\"<PERSON><PERSON> fiel\",\"<PERSON><PERSON><PERSON> obediente\",\"<PERSON><PERSON> justo\",\"<PERSON><PERSON><PERSON> virtuosa\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "<PERSON> 1:12", "update_time": null, "id": "189", "level": "188", "question": "Que mulher se recusou a exibir sua beleza diante dos convidados de seu marido a seu comando?", "count": "4", "quiz_index": "219", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON> de Sa<PERSON>á\",\"<PERSON><PERSON><PERSON>\"]"}, {"answer": "Adore a está<PERSON>a", "ref": "Daniel 3:13-14", "update_time": null, "id": "190", "level": "189", "question": "O rei Nabucodonosor ficou zangado com Sadraque, <PERSON><PERSON> e <PERSON>dnego, porque eles se recusam a ___.", "count": "4", "quiz_index": "211", "error": "[\"Interpretar seu sonho\",\"Pregar\",\"<PERSON>ore a estátua\",\"Adoração a Deus\"]"}, {"answer": "Lobos", "ref": "<PERSON> 7:15", "update_time": null, "id": "191", "level": "190", "question": "Que tipo de animal Jesus se refere aos falsos profetas?", "count": "4", "quiz_index": "244", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>vel<PERSON>\",\"Cobras\",\"Lobos\"]"}, {"answer": "Pedra", "ref": "Exodus 17:4", "update_time": null, "id": "192", "level": "191", "question": "E Moisés clamou a<PERSON>, dizendo: Que farei a este povo? Eles estão quase prontos para me ___.", "count": "4", "quiz_index": "258", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Pedra\",\"Vegetal\"]"}, {"answer": "<PERSON><PERSON><PERSON>", "ref": "Galatians 1:8", "update_time": null, "id": "193", "level": "192", "question": "As igrejas da Galácia estavam ouvindo outro ___.", "count": "4", "quiz_index": "254", "error": "[\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"História\",\"Palavra\"]"}, {"answer": "O sinete do rei", "ref": "<PERSON> 6:17", "update_time": null, "id": "194", "level": "193", "question": "O que foi usado para selar a cova depois que Daniel foi jogado?", "count": "4", "quiz_index": "237", "error": "[\"Barras de ferro\",\"A espada do rei\",\"O sinete do rei\",\"<PERSON><PERSON><PERSON> molhada\"]"}, {"answer": "O homem à sua imagem", "ref": "Genesis 1:24-31", "update_time": null, "id": "195", "level": "194", "question": "No 'sexto dia' o que Deus criou?", "count": "4", "quiz_index": "251", "error": "[\"<PERSON><PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"O homem à sua imagem\"]"}]