#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验数据分析脚本
分析结果题目中的实验数据是否符合实验组的要求
"""

import json
import re
from typing import Dict, List, Set, Tuple

def load_json_file(filepath: str) -> List[Dict]:
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def parse_experiment_groups(filepath: str) -> Dict[str, List[List[str]]]:
    """解析实验组文件"""
    groups = {"en": [], "pt": []}
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析英语实验组
        en_matches = re.findall(r'英语语言建议（([^）]+)）', content)
        for match in en_matches:
            # 清理数字，去除前导零
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["en"].append(ids)
        
        # 解析葡萄牙语实验组
        pt_matches = re.findall(r'葡萄牙语言建议（([^）]+)）', content)
        for match in pt_matches:
            # 清理数字，去除前导零
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["pt"].append(ids)
            
    except Exception as e:
        print(f"Error parsing experiment groups: {e}")
    
    return groups

def create_source_id_mapping(source_data: List[Dict]) -> Dict[str, Dict]:
    """创建源数据ID映射"""
    mapping = {}
    for item in source_data:
        mapping[item["id"]] = item
    return mapping

def analyze_result_file(result_file: str, source_mapping: Dict[str, Dict], 
                       expected_ids: List[str], group_name: str) -> Dict:
    """分析单个结果文件"""
    result_data = load_json_file(result_file)
    analysis = {
        "file": result_file,
        "group": group_name,
        "expected_ids": expected_ids,
        "found_ids": [],
        "missing_ids": [],
        "extra_ids": [],
        "field_mapping_errors": [],
        "answer_verification": [],
        "total_questions": len(result_data)
    }
    
    found_ids_set = set()
    
    for item in result_data:
        # 检查difficulty字段（这应该对应实验组的id）
        difficulty_id = str(item.get("difficulty", ""))
        found_ids_set.add(difficulty_id)
        analysis["found_ids"].append(difficulty_id)

        # 验证字段映射 - 使用difficulty字段来查找源数据
        if difficulty_id in source_mapping:
            source_item = source_mapping[difficulty_id]
            
            # 验证答案
            source_answer = source_item.get("answer", "")
            result_answer = item.get("rightAnswer", "")
            
            answer_check = {
                "difficulty_id": difficulty_id,
                "source_answer": source_answer,
                "result_answer": result_answer,
                "match": source_answer == result_answer
            }
            analysis["answer_verification"].append(answer_check)

            # 验证题目内容
            source_question = source_item.get("question", "")
            result_question = item.get("quizTitle", "")

            if source_question != result_question:
                analysis["field_mapping_errors"].append({
                    "difficulty_id": difficulty_id,
                    "field": "question",
                    "source": source_question,
                    "result": result_question
                })
        else:
            analysis["field_mapping_errors"].append({
                "difficulty_id": difficulty_id,
                "error": "Source ID not found in source data"
            })
    
    # 检查缺失和多余的ID
    expected_ids_set = set(expected_ids)
    analysis["missing_ids"] = list(expected_ids_set - found_ids_set)
    analysis["extra_ids"] = list(found_ids_set - expected_ids_set)
    
    return analysis

def main():
    print("=== 实验数据分析报告 ===\n")
    
    # 1. 加载实验组规则
    print("1. 加载实验组规则...")
    experiment_groups = parse_experiment_groups("实验组.txt")
    print(f"英语实验组: {experiment_groups['en']}")
    print(f"葡萄牙语实验组: {experiment_groups['pt']}\n")
    
    # 2. 加载源数据
    print("2. 加载源数据...")
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    en_source_mapping = create_source_id_mapping(en_source)
    pt_source_mapping = create_source_id_mapping(pt_source)
    
    print(f"英语源题目数量: {len(en_source)}")
    print(f"葡萄牙语源题目数量: {len(pt_source)}\n")
    
    # 3. 分析结果文件
    print("3. 分析结果文件...\n")
    
    # 英语组分析
    en_groups = ["A", "B", "C"]
    for i, group in enumerate(en_groups):
        if i < len(experiment_groups["en"]):
            expected_ids = experiment_groups["en"][i]
            result_file = f"结果题目/en_{group}.json"
            analysis = analyze_result_file(result_file, en_source_mapping, 
                                         expected_ids, f"英语{group}组")
            print_analysis_result(analysis)
    
    # 葡萄牙语组分析
    pt_groups = ["A", "B", "C"]
    for i, group in enumerate(pt_groups):
        if i < len(experiment_groups["pt"]):
            expected_ids = experiment_groups["pt"][i]
            result_file = f"结果题目/pt_{group}.json"
            analysis = analyze_result_file(result_file, pt_source_mapping, 
                                         expected_ids, f"葡萄牙语{group}组")
            print_analysis_result(analysis)

def print_analysis_result(analysis: Dict):
    """打印分析结果"""
    print(f"=== {analysis['group']} 分析结果 ===")
    print(f"文件: {analysis['file']}")
    print(f"题目总数: {analysis['total_questions']}")
    print(f"期望ID: {analysis['expected_ids']}")
    print(f"实际ID: {analysis['found_ids']}")
    
    # 检查ID匹配情况
    if analysis['missing_ids']:
        print(f"❌ 缺失ID: {analysis['missing_ids']}")
    else:
        print("✅ 没有缺失ID")
    
    if analysis['extra_ids']:
        print(f"❌ 多余ID: {analysis['extra_ids']}")
    else:
        print("✅ 没有多余ID")
    
    # 检查答案验证
    correct_answers = sum(1 for item in analysis['answer_verification'] if item['match'])
    total_answers = len(analysis['answer_verification'])
    print(f"答案验证: {correct_answers}/{total_answers} 正确")
    
    if correct_answers != total_answers:
        print("❌ 答案不匹配的题目:")
        for item in analysis['answer_verification']:
            if not item['match']:
                print(f"  ID {item['replace_id']}: 源答案='{item['source_answer']}', 结果答案='{item['result_answer']}'")
    else:
        print("✅ 所有答案都正确匹配")
    
    # 检查字段映射错误
    if analysis['field_mapping_errors']:
        print("❌ 字段映射错误:")
        for error in analysis['field_mapping_errors']:
            print(f"  {error}")
    else:
        print("✅ 字段映射正确")
    
    print()

if __name__ == "__main__":
    main()
