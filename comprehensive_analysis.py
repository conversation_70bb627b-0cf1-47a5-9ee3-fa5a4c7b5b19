#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果题目全面分析脚本
重新分析结果题目的数据结构、映射关系和实验组匹配情况
"""

import json
import re

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def parse_experiment_groups():
    """解析实验组规则"""
    groups = {"en": [], "pt": []}
    
    try:
        with open('实验组.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析英语实验组
        en_matches = re.findall(r'英语语言建议（([^）]+)）', content)
        for match in en_matches:
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["en"].append(ids)
        
        # 解析葡萄牙语实验组
        pt_matches = re.findall(r'葡萄牙语言建议（([^）]+)）', content)
        for match in pt_matches:
            ids = [str(int(id.strip())) for id in match.split('，')]
            groups["pt"].append(ids)
            
    except Exception as e:
        print(f"Error parsing experiment groups: {e}")
    
    return groups

def create_source_mapping(source_data):
    """创建源数据映射"""
    mapping = {}
    for item in source_data:
        mapping[item["id"]] = item
    return mapping

def analyze_single_result_file(file_path, group_name, expected_ids, source_mapping):
    """分析单个结果文件"""
    print(f"\n=== {group_name} 详细分析 ===")
    result_data = load_json_file(file_path)
    
    if not result_data:
        print("无法加载数据")
        return
    
    print(f"文件: {file_path}")
    print(f"题目总数: {len(result_data)}")
    print(f"期望ID: {expected_ids}")
    
    # 分析各种ID字段
    analysis = {
        "id_fields": [],
        "difficulty_fields": [],
        "replace_id_fields": [],
        "quiz_id_fields": [],
        "field_mappings": [],
        "answer_verifications": []
    }
    
    for i, item in enumerate(result_data):
        print(f"\n--- 题目 {i+1} ---")
        
        # 提取各种ID字段
        id_field = item.get("id", "")
        difficulty_field = str(item.get("difficulty", ""))
        replace_id_field = str(item.get("replaceId", ""))
        quiz_id_field = item.get("quizId", "")
        
        analysis["id_fields"].append(id_field)
        analysis["difficulty_fields"].append(difficulty_field)
        analysis["replace_id_fields"].append(replace_id_field)
        analysis["quiz_id_fields"].append(quiz_id_field)
        
        print(f"id: {id_field}")
        print(f"quizId: {quiz_id_field}")
        print(f"difficulty: {difficulty_field}")
        print(f"replaceId: {replace_id_field}")
        
        # 检查哪个字段匹配实验组要求
        difficulty_in_expected = difficulty_field in expected_ids
        replace_id_in_expected = replace_id_field in expected_ids
        
        print(f"difficulty在期望ID中: {difficulty_in_expected}")
        print(f"replaceId在期望ID中: {replace_id_in_expected}")
        
        # 验证与源数据的映射
        if difficulty_field in source_mapping:
            source_item = source_mapping[difficulty_field]
            print(f"difficulty对应源题目: ID={difficulty_field}")
            print(f"  源题目: {source_item.get('question', '')[:50]}...")
            print(f"  结果题目: {item.get('quizTitle', '')[:50]}...")
            print(f"  题目匹配: {source_item.get('question', '') == item.get('quizTitle', '')}")
            print(f"  答案匹配: {source_item.get('answer', '') == item.get('rightAnswer', '')}")
        
        if replace_id_field in source_mapping and replace_id_field != difficulty_field:
            source_item = source_mapping[replace_id_field]
            print(f"replaceId对应源题目: ID={replace_id_field}")
            print(f"  源题目: {source_item.get('question', '')[:50]}...")
    
    # 总结分析
    print(f"\n=== {group_name} 总结 ===")
    print(f"所有id字段: {analysis['id_fields']}")
    print(f"所有difficulty字段: {analysis['difficulty_fields']}")
    print(f"所有replaceId字段: {analysis['replace_id_fields']}")
    
    # 检查哪个字段匹配实验组
    difficulty_matches = set(analysis['difficulty_fields']) == set(expected_ids)
    replace_id_matches = set(analysis['replace_id_fields']) == set(expected_ids)
    
    print(f"\ndifficulty字段匹配实验组: {difficulty_matches}")
    print(f"replaceId字段匹配实验组: {replace_id_matches}")
    
    if difficulty_matches:
        print("✅ difficulty字段正确对应实验组要求")
    elif replace_id_matches:
        print("✅ replaceId字段正确对应实验组要求")
    else:
        print("❌ 没有字段完全匹配实验组要求")

def main():
    print("=== 结果题目全面重新分析 ===")
    
    # 1. 加载实验组规则
    experiment_groups = parse_experiment_groups()
    print(f"\n实验组规则:")
    print(f"英语: {experiment_groups['en']}")
    print(f"葡萄牙语: {experiment_groups['pt']}")
    
    # 2. 加载源数据
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    en_source_mapping = create_source_mapping(en_source)
    pt_source_mapping = create_source_mapping(pt_source)
    
    print(f"\n源数据:")
    print(f"英语源题目: {len(en_source)}个")
    print(f"葡萄牙语源题目: {len(pt_source)}个")
    
    # 3. 分析结果文件
    result_files = [
        ("结果题目/en_A.json", "英语A组", experiment_groups["en"][0], en_source_mapping),
        ("结果题目/en_B.json", "英语B组", experiment_groups["en"][1], en_source_mapping),
        ("结果题目/en_C.json", "英语C组", experiment_groups["en"][2], en_source_mapping),
        ("结果题目/pt_A.json", "葡萄牙语A组", experiment_groups["pt"][0], pt_source_mapping),
        ("结果题目/pt_B.json", "葡萄牙语B组", experiment_groups["pt"][1], pt_source_mapping),
        ("结果题目/pt_C.json", "葡萄牙语C组", experiment_groups["pt"][2], pt_source_mapping)
    ]
    
    for file_path, group_name, expected_ids, source_mapping in result_files:
        analyze_single_result_file(file_path, group_name, expected_ids, source_mapping)

if __name__ == "__main__":
    main()
