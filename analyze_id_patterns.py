#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析结果题目中的ID模式
检查结果题目中的id字段与源题目中的id字段的关系
"""

import json

def load_json_file(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def analyze_id_patterns():
    """分析ID模式"""
    print("=== ID模式分析报告 ===\n")
    
    # 加载源数据
    en_source = load_json_file("源题目/en.json")
    pt_source = load_json_file("源题目/pt.json")
    
    # 创建源数据ID集合
    en_source_ids = set(item["id"] for item in en_source)
    pt_source_ids = set(item["id"] for item in pt_source)
    
    print(f"英语源题目ID范围: {min(en_source_ids)} - {max(en_source_ids)}")
    print(f"葡萄牙语源题目ID范围: {min(pt_source_ids)} - {max(pt_source_ids)}")
    print()
    
    # 分析结果文件
    result_files = [
        ("结果题目/en_A.json", "英语A组", en_source_ids),
        ("结果题目/en_B.json", "英语B组", en_source_ids),
        ("结果题目/en_C.json", "英语C组", en_source_ids),
        ("结果题目/pt_A.json", "葡萄牙语A组", pt_source_ids),
        ("结果题目/pt_B.json", "葡萄牙语B组", pt_source_ids),
        ("结果题目/pt_C.json", "葡萄牙语C组", pt_source_ids)
    ]
    
    for file_path, group_name, source_ids in result_files:
        print(f"=== {group_name} ===")
        result_data = load_json_file(file_path)
        
        if not result_data:
            print("无法加载数据\n")
            continue
        
        print(f"题目数量: {len(result_data)}")
        
        # 分析各种ID字段
        result_ids = []
        quiz_ids = []
        difficulty_ids = []
        replace_ids = []
        
        for item in result_data:
            result_ids.append(item.get("id", ""))
            quiz_ids.append(item.get("quizId", ""))
            difficulty_ids.append(str(item.get("difficulty", "")))
            replace_ids.append(str(item.get("replaceId", "")))
        
        print(f"结果题目id字段: {result_ids}")
        print(f"结果题目quizId字段: {quiz_ids}")
        print(f"结果题目difficulty字段: {difficulty_ids}")
        print(f"结果题目replaceId字段: {replace_ids}")
        
        # 检查哪些ID在源数据中存在
        result_ids_in_source = [id for id in result_ids if id in source_ids]
        difficulty_ids_in_source = [id for id in difficulty_ids if id in source_ids]
        replace_ids_in_source = [id for id in replace_ids if id in source_ids]
        
        print(f"id字段在源数据中存在的: {result_ids_in_source} ({len(result_ids_in_source)}/{len(result_ids)})")
        print(f"difficulty字段在源数据中存在的: {difficulty_ids_in_source} ({len(difficulty_ids_in_source)}/{len(difficulty_ids)})")
        print(f"replace_ids字段在源数据中存在的: {replace_ids_in_source} ({len(replace_ids_in_source)}/{len(replace_ids)})")
        
        # 检查ID是否重复使用
        if len(set(result_ids)) != len(result_ids):
            print("⚠️ 结果题目id字段有重复")
        else:
            print("✅ 结果题目id字段无重复")
        
        print()

def analyze_id_generation_pattern():
    """分析ID生成模式"""
    print("=== ID生成模式分析 ===\n")
    
    all_result_ids = []
    
    # 收集所有结果文件的ID
    result_files = [
        "结果题目/en_A.json",
        "结果题目/en_B.json", 
        "结果题目/en_C.json",
        "结果题目/pt_A.json",
        "结果题目/pt_B.json",
        "结果题目/pt_C.json"
    ]
    
    for file_path in result_files:
        result_data = load_json_file(file_path)
        for item in result_data:
            all_result_ids.append(int(item.get("id", "0")))
    
    all_result_ids.sort()
    
    print(f"所有结果题目ID: {all_result_ids}")
    print(f"ID范围: {min(all_result_ids)} - {max(all_result_ids)}")
    print(f"ID总数: {len(all_result_ids)}")
    print(f"唯一ID数: {len(set(all_result_ids))}")
    
    # 检查是否是连续的
    expected_sequence = list(range(min(all_result_ids), max(all_result_ids) + 1))
    if all_result_ids == expected_sequence:
        print("✅ ID是连续的序列")
    else:
        missing = set(expected_sequence) - set(all_result_ids)
        extra = set(all_result_ids) - set(expected_sequence)
        if missing:
            print(f"❌ 缺失的ID: {sorted(missing)}")
        if extra:
            print(f"❌ 多余的ID: {sorted(extra)}")
    
    print()

if __name__ == "__main__":
    analyze_id_patterns()
    analyze_id_generation_pattern()
